# Playwright Automation Framework

An AI-powered web automation framework using <PERSON><PERSON>, <PERSON><PERSON>, and Maven with custom reporting and test generation capabilities.

## 🚀 Features

- **AI-Powered Test Generation**: Generate test cases from natural language prompts using OpenAI/LangChain
- **Playwright Integration**: Modern, fast, and reliable web automation
- **TestNG Framework**: Robust test execution with parallel support
- **Custom Reporting**: ExtentReports and Allure integration with screenshots
- **Maven Build System**: Easy dependency management and build automation
- **Multi-Browser Support**: Chromium, Firefox, and WebKit
- **Configuration Management**: Environment-specific configurations
- **Screenshot Management**: Automatic failure screenshots and custom captures
- **API Testing**: Built-in HTTP client for API testing
- **Page Object Model**: Structured and maintainable test architecture

## 📋 Prerequisites

- Java 11 or higher
- Maven 3.6 or higher
- Node.js (for Playwright browser installation)

## 🛠️ Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd playwright-automation-framework
```

### 2. Install Dependencies
```bash
mvn clean install
```

### 3. Install Playwright Browsers
```bash
mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install"
```

### 4. Configure Environment
Edit `src/main/resources/config.properties` or create environment-specific files:
- `config-dev.properties`
- `config-staging.properties`
- `config-prod.properties`

### 5. Set API Keys (Optional)
For AI test generation, add your API keys to the configuration:
```properties
openai.api.key=your_openai_api_key
augment.api.key=your_augment_api_key
```

## 🏃‍♂️ Running Tests

### Run All Tests
```bash
mvn test
```

### Run Specific Test Suite
```bash
mvn test -Dtest=SmokeTests
mvn test -Dtest=GeneratedTests
mvn test -Dtest=APITests
```

### Run with Specific Browser
```bash
mvn test -Dautomation.browser=firefox
mvn test -Dautomation.browser=webkit
```

### Run in Headless Mode
```bash
mvn test -Dautomation.headless=true
```

### Run with Environment
```bash
mvn test -Denvironment=staging
```

## 📊 Reports

After test execution, reports are generated in:
- **ExtentReports**: `test-results/reports/ExtentReport_[timestamp].html`
- **Allure Reports**: `target/allure-results/`
- **Screenshots**: `test-results/screenshots/`
- **Videos**: `test-results/videos/` (if enabled)

### Generate Allure Report
```bash
mvn allure:serve
```

## 🤖 AI Test Generation

### Using the Test Generator
```java
TestGenerator generator = new TestGenerator();
List<TestCase> testCases = generator.generateTestCases(
    "Navigate to Google and search for 'Playwright automation'",
    "https://www.google.com"
);
```

### Example Prompts
- "Login with username 'admin' and password 'password123'"
- "Fill out contact form and submit"
- "Search for 'automation testing' and verify results"
- "Navigate to checkout and complete purchase"

## 📁 Project Structure

```
src/
├── main/java/com/automation/
│   ├── core/                 # Core framework components
│   │   ├── DriverManager.java
│   │   └── TestBase.java
│   ├── config/               # Configuration management
│   │   └── ConfigManager.java
│   ├── generator/            # AI test generation
│   │   ├── TestGenerator.java
│   │   └── PromptProcessor.java
│   ├── models/               # Data models
│   │   ├── TestCase.java
│   │   └── TestStep.java
│   ├── reporting/            # Custom reporting
│   │   └── ExtentManager.java
│   ├── utils/                # Utility classes
│   │   └── ScreenshotUtils.java
│   └── listeners/            # TestNG listeners
│       ├── TestListener.java
│       ├── AllureListener.java
│       └── ExtentReportListener.java
├── test/java/com/automation/tests/
│   ├── SmokeTests.java       # Basic smoke tests
│   ├── GeneratedTests.java   # AI-generated tests
│   ├── APITests.java         # API testing examples
│   └── generated/            # Auto-generated test classes
└── main/resources/
    ├── config.properties     # Default configuration
    ├── config-dev.properties # Development config
    └── logback.xml          # Logging configuration
```

## ⚙️ Configuration

### Browser Configuration
```properties
browser=chromium                    # chromium, firefox, webkit
headless=false                     # true for headless mode
browser.args=--disable-web-security # Additional browser arguments
```

### Timeout Configuration
```properties
default.timeout=30000              # Default timeout in milliseconds
page.load.timeout=60000            # Page load timeout
element.timeout=10000              # Element wait timeout
```

### Reporting Configuration
```properties
screenshot.on.failure=true         # Capture screenshots on failure
screenshot.on.success=false        # Capture screenshots on success
video.recording=false              # Enable video recording
```

## 🧪 Writing Tests

### Basic Test Structure
```java
public class MyTests extends TestBase {

    @Test(description = "Test description", groups = {"smoke"})
    public void myTest() {
        // Navigate to page
        navigateTo("https://example.com");

        // Interact with elements
        getPage().click("button#submit");
        getPage().fill("input[name='username']", "testuser");

        // Assertions
        PlaywrightAssertions.assertThat(getPage().locator(".success")).isVisible();

        // Custom logging
        logTestPass("Test completed successfully");

        // Take screenshot
        takeScreenshot("Test completion");
    }
}
```

### Using Page Object Model
```java
public class LoginPage {
    private final Page page;

    public LoginPage(Page page) {
        this.page = page;
    }

    public void login(String username, String password) {
        page.fill("#username", username);
        page.fill("#password", password);
        page.click("#login-button");
    }

    public boolean isLoggedIn() {
        return page.isVisible(".dashboard");
    }
}
```

## 🔧 Advanced Features

### Parallel Execution
Configure parallel execution in `testng.xml`:
```xml
<suite name="ParallelSuite" parallel="methods" thread-count="3">
```

### Custom Assertions
```java
// Custom assertion with screenshot
public void assertElementVisible(String selector, String message) {
    try {
        PlaywrightAssertions.assertThat(getPage().locator(selector)).isVisible();
        logTestPass(message);
    } catch (AssertionError e) {
        takeScreenshot("Assertion_Failed_" + selector);
        logTestFail(message + " - " + e.getMessage());
        throw e;
    }
}
```

### Data-Driven Testing
```java
@DataProvider(name = "loginData")
public Object[][] getLoginData() {
    return new Object[][] {
        {"user1", "pass1"},
        {"user2", "pass2"},
        {"user3", "pass3"}
    };
}

@Test(dataProvider = "loginData")
public void testLogin(String username, String password) {
    // Test implementation
}
```

## 🚀 CI/CD Integration

### GitHub Actions Example
```yaml
name: Automation Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v2
        with:
          java-version: '11'
      - name: Run tests
        run: |
          mvn clean test -Dautomation.headless=true
      - name: Generate Allure Report
        run: mvn allure:report
      - name: Upload Reports
        uses: actions/upload-artifact@v2
        with:
          name: test-reports
          path: target/allure-results/
```

## 🐛 Troubleshooting

### Common Issues

1. **Browser Installation Issues**
   ```bash
   mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install --force"
   ```

2. **Permission Issues on Linux/Mac**
   ```bash
   chmod +x mvnw
   ```

3. **Memory Issues**
   ```bash
   export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"
   ```

4. **Timeout Issues**
   - Increase timeouts in `config.properties`
   - Use explicit waits in tests
   - Check network connectivity

### Debug Mode
```bash
mvn test -Dautomation.log.level=DEBUG -Dautomation.headless=false
```

## 📚 Documentation

- [Playwright Documentation](https://playwright.dev/java/)
- [TestNG Documentation](https://testng.org/doc/)
- [ExtentReports Documentation](https://extentreports.com/)
- [Allure Documentation](https://docs.qameta.io/allure/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the documentation links

## 🔄 Version History

- **v1.0.0** - Initial release with core features
- **v1.1.0** - Added AI test generation
- **v1.2.0** - Enhanced reporting and parallel execution
