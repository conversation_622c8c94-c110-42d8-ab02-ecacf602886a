# Playwright Automation Framework

An AI-powered web automation framework using <PERSON><PERSON>, <PERSON><PERSON>, and Maven with custom reporting and test generation capabilities.

## 🚀 Features

- **AI-Powered Test Generation**: Generate test cases from natural language prompts using OpenAI/LangChain
- **Playwright Integration**: Modern, fast, and reliable web automation
- **TestNG Framework**: Robust test execution with parallel support
- **Custom Reporting**: ExtentReports and Allure integration with screenshots
- **Maven Build System**: Easy dependency management and build automation
- **Multi-Browser Support**: Chromium, Firefox, and WebKit
- **Configuration Management**: Environment-specific configurations
- **Screenshot Management**: Automatic failure screenshots and custom captures
- **API Testing**: Built-in HTTP client for API testing
- **Page Object Model**: Structured and maintainable test architecture

## 📋 Prerequisites

- Java 11 or higher
- Maven 3.6 or higher
- Node.js (for Playwright browser installation)

## 🛠️ Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd playwright-automation-framework
```

### 2. Install Dependencies
```bash
mvn clean install
```

### 3. Install Playwright Browsers
```bash
mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install"
```

### 4. Configure Environment
Edit `src/main/resources/config.properties` or create environment-specific files:
- `config-dev.properties`
- `config-staging.properties`
- `config-prod.properties`

### 5. Set API Keys (Optional)
For AI test generation, add your API keys to the configuration:
```properties
openai.api.key=your_openai_api_key
augment.api.key=your_augment_api_key
```

## 🏃‍♂️ Running Tests

### Run All Tests
```bash
mvn test
```

### Run Specific Test Suite
```bash
mvn test -Dtest=SmokeTests
mvn test -Dtest=GeneratedTests
mvn test -Dtest=APITests
```

### Run with Specific Browser
```bash
mvn test -Dautomation.browser=firefox
mvn test -Dautomation.browser=webkit
```

### Run in Headless Mode
```bash
mvn test -Dautomation.headless=true
```

### Run with Environment
```bash
mvn test -Denvironment=staging
```

## 📊 Reports

After test execution, reports are generated in:
- **ExtentReports**: `test-results/reports/ExtentReport_[timestamp].html`
- **Allure Reports**: `target/allure-results/`
- **Screenshots**: `test-results/screenshots/`
- **Videos**: `test-results/videos/` (if enabled)

### Generate Allure Report
```bash
mvn allure:serve
```

## 🤖 AI Test Generation

### Using the Test Generator
```java
TestGenerator generator = new TestGenerator();
List<TestCase> testCases = generator.generateTestCases(
    "Navigate to Google and search for 'Playwright automation'",
    "https://www.google.com"
);
```

### Example Prompts
- "Login with username 'admin' and password 'password123'"
- "Fill out contact form and submit"
- "Search for 'automation testing' and verify results"
- "Navigate to checkout and complete purchase"

## 📁 Project Structure

```
src/
├── main/java/com/automation/
│   ├── core/                 # Core framework components
│   │   ├── DriverManager.java
│   │   └── TestBase.java
│   ├── config/               # Configuration management
│   │   └── ConfigManager.java
│   ├── generator/            # AI test generation
│   │   ├── TestGenerator.java
│   │   └── PromptProcessor.java
│   ├── models/               # Data models
│   │   ├── TestCase.java
│   │   └── TestStep.java
│   ├── reporting/            # Custom reporting
│   │   └── ExtentManager.java
│   ├── utils/                # Utility classes
│   │   └── ScreenshotUtils.java
│   └── listeners/            # TestNG listeners
│       ├── TestListener.java
│       ├── AllureListener.java
│       └── ExtentReportListener.java
├── test/java/com/automation/tests/
│   ├── SmokeTests.java       # Basic smoke tests
│   ├── GeneratedTests.java   # AI-generated tests
│   ├── APITests.java         # API testing examples
│   └── generated/            # Auto-generated test classes
└── main/resources/
    ├── config.properties     # Default configuration
    ├── config-dev.properties # Development config
    └── logback.xml          # Logging configuration
```
