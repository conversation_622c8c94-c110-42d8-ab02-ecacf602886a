import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchTestStepsRequest } from "../store/actions";

const TestCasesTab = () => {
  const dispatch = useDispatch();
  const testCases = useSelector((state) => state.testCases);
  const [filter, setFilter] = useState("all");

  useEffect(() => {
    dispatch(fetchTestStepsRequest());
  }, [dispatch]);


//   const testCases = {
//     "summary": "[FE] Make a no-cache call to PIM after the addon activation/deactivation and contract termination",
//     "issueKey": "MMKC2-12612",
//     "testSteps": [
//         "h3. Description\r",
//         "\r",
//         "PIM keeps the products for up to 15 min in its DB. For cases where no cache should be used, OneX channels need to send a request with the respective `cache-control` header (set to `no-cache`).\r",
//         "\r",
//         "Therefore, we need to have the cache-control header in the `contract details` and `available addons` BFF endpoints so that, when it is set by the FE, the PIM call that is part of these endpoints will be done with `no-cache` in the `cache-control` header.\r",
//         "\r",
//         "The UI should set this request header to 'no-cache' whenever we need to refresh the product data (e.g., after a contract termination, after an addon activation/deactivation, etc.).\r",
//         "h3. Acceptance criteria\r",
//         "\r",
//         "- FE should be setting the 'cache-control' header to 'no-cache' in every scenario that it's necessary to fetch fresh data from PIM (contract termination, cancelation, addon activation/deactivation etc.)"
//     ]
// }

// const filteredTestCases = testCases.filter((tc) => {
//   if (filter === "all") return true;
//   return tc.type === filter;
// });

  return (
    <div>
      {/* Filter Buttons */}
      <div className="filter-buttons">
        <button
          className={`filter-btn ${filter === "all" ? "active" : ""}`}
          onClick={() => setFilter("all")}
        >
          All
        </button>
        <button
          className={`filter-btn ${filter === "MMKC" ? "active" : ""}`}
          onClick={() => setFilter("MMKC")}
        >
          MMKC
        </button>
        <button
          className={`filter-btn ${filter === "MAVI" ? "active" : ""}`}
          onClick={() => setFilter("MAVI")}
        >
          MAVI
        </button>
      </div>

      {/* Render Filtered Test Cases */}
      {/* {filteredTestCases.map((tc, idx) => ( */}
        <div
          // key={idx}
          className="mb-6 p-4 rounded-xl bg-slate-700 text-white shadow-lg"
        >
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-bold">{testCases?.summary}</h3>
            {/* <span className="text-sm text-purple-300">
              {tc.type.toUpperCase()}
            </span> */}
          </div>
          {/* <p className="mt-2 text-gray-300">{tc.description}</p> */}
          {/* <ul className="list-disc pl-5 mt-2 text-gray-400 text-sm"> */}
            {/* {tc.steps.map((step, i) => ( */}
              <div
              // key={i}
              >{testCases?.testSteps}</div>
            {/* ))} */}
          {/* </ul> */}

          {/* <details className="mt-4 text-sm bg-black p-2 rounded-lg">
            <summary className="cursor-pointer text-pink-400">Show Code</summary>
            <pre className="whitespace-pre-wrap text-white mt-2">{tc.code}</pre>
          </details> */}
        </div>
      {/* ))} */}
    </div>
  );
};

export default TestCasesTab;
