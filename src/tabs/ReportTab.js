import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchReportRequest } from "../store/actions";

const ReportTab = () => {
  const dispatch = useDispatch();
  const htmlReport = useSelector((state) => state.htmlReport);

  useEffect(() => {
    dispatch(fetchReportRequest());
  }, [dispatch]);

  return (
    <div className="report-section">
      <h2 className="custom-heading-report">Test Run Report</h2>
      {htmlReport ? (
        <div
          className="custom-container-report"
          dangerouslySetInnerHTML={{ __html: htmlReport }}
        />
      ) : (
        <p className="text-slate-400">Loading report...</p>
      )}
    </div>
  );
};

export default ReportTab;
