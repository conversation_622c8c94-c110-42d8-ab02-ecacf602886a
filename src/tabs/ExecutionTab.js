import React, { useState, useEffect, useRef } from "react";
import { Chart } from "chart.js/auto";
import { useDispatch, useSelector } from "react-redux";
import { fetchHistoryRequest } from "../store/actions";

const ExecutionTab = () => {
  const dispatch = useDispatch();
  const history = useSelector((state) => state.history);
  const [expanded, setExpanded] = useState(null);
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  const chartConfig = {
    type: "line",
    data: {
      labels: ["Apr 25", "Apr 26", "Apr 27", "Apr 28", "Apr 29"],
      datasets: [
        {
          label: "Passed",
          data: [8, 9, 7, 10, 9],
          borderColor: "#10b981",
          backgroundColor: "rgba(16, 185, 129, 0.2)",
          fill: true,
        },
        {
          label: "Failed",
          data: [2, 1, 3, 0, 1],
          borderColor: "#ef4444",
          backgroundColor: "rgba(239, 68, 68, 0.2)",
          fill: true,
        },
      ],
    },
    options: {
      plugins: {
        title: {
          display: true,
          text: "Execution Trends - Last 5 Days",
          color: "#f1f5f9",
          font: { size: 18 },
        },
        legend: {
          labels: { color: "#f1f5f9" },
        },
      },
      scales: {
        x: { ticks: { color: "#f1f5f9" }, grid: { color: "#334155" } },
        y: { ticks: { color: "#f1f5f9" }, grid: { color: "#334155" } },
      },
    },
  };

  useEffect(() => {
    dispatch(fetchHistoryRequest());
    if (chartRef.current && !chartInstance.current) {
      chartInstance.current = new Chart(chartRef.current, chartConfig);
    }
  }, [dispatch]);

  const toggleExpand = (index) => {
    setExpanded(expanded === index ? null : index);
  };

  return (
    <div className="bg-slate-800 p-6 rounded-2xl shadow-xl">
      <h2 className="text-xl font-bold mb-4 text-pink-300">Execution Trend</h2>
      <canvas ref={chartRef} height="150"></canvas>
      <h2 className="text-xl font-bold mb-4 text-pink-300">
        Execution History
      </h2>
      {history.length === 0 ? (
        <p className="text-slate-400">No test run yet.</p>
      ) : (
        <ul className="space-y-4">
          {history.map((run, idx) => (
            <li key={idx}>
              <div
                onClick={() => toggleExpand(idx)}
                className="cursor-pointer text-slate-200 hover:text-pink-300"
              >
                <b>{run.timestamp}</b> -{" "}
                <span
                  className={`${
                    run.status === "Passed" ? "text-green-400" : "text-red-400"
                  }`}
                >
                  {run.status}
                </span>
                <button className="ml-3 text-xs bg-slate-600 px-2 py-1 rounded-md">
                  {expanded === idx ? "▲ Hide Logs" : "▼ Show Logs"}
                </button>
              </div>
              {expanded === idx && (
                <div className="bg-slate-700 mt-2 p-2 rounded-md text-sm space-y-1">
                  {run.logs.map((log, i) => (
                    <div key={i}>👉 {log}</div>
                  ))}
                </div>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ExecutionTab;
