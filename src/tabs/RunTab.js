import React, {  useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchReportRequest,
  runTestRequest,
  setLiveLogs,
  abortTestRequest,
  setTestRunning,
  setActiveTab,
} from "../store/actions";
import AgentRunner from "../components/AggentRunner";

const RunTab = () => {
  const dispatch = useDispatch();
  const loading = useSelector((state) => state.loading);
  const liveLogs = useSelector((state) => state.liveLogs);
  const logEndRef = useRef(null);

  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [liveLogs]);

  const runTest = async () => {
    try {
      dispatch(runTestRequest());
      dispatch(fetchReportRequest());
      dispatch(setActiveTab("execution"));
    } catch (err) {
      console.error("Test run failed", err);
      setTestRunning(false);
    }
  };

  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [liveLogs]);

  const abortTest = () => {
    dispatch(abortTestRequest()).then(res=>{
      if (res.ok) {
        dispatch(setTestRunning(false)); // Mark the test as stopped
        dispatch(setLiveLogs([...liveLogs, "Test Aborted"]));
      }
    }).catch(err=>{
      console.error("Failed to abort test", err);
    });

  };

  return (
    <div className="bg-slate-800 p-6 rounded-xl shadow-md">
      <AgentRunner/>
      <div className="run-tab">
        <button
          onClick={runTest}
          disabled={loading}
          className="bg-gradient-to-r from-pink-500 to-fuchsia-600 hover:opacity-90 text-white font-bold text-lg px-8 py-3 rounded-2xl shadow-lg transition"
        >
          {loading ? "⏳ Running..." : "▶️ Run All Tests"}
        </button>
        <button
          onClick={abortTest}
          className="bg-gradient-to-r from-red-500 to-red-600 hover:opacity-90 text-white font-bold text-lg px-8 py-3 rounded-2xl shadow-lg transition"
        >
          🛑 Abort Test
        </button>
      </div>
      <div className="console-box max-h-80 overflow-auto bg-slate-900 text-slate-100 rounded-md p-4 shadow-inner">
        <h3 className="text-pink-300 font-semibold mb-3">🔴 Live Console</h3>
        {liveLogs.length === 0 ? (
          <p className="text-slate-400">
            No logs yet. Click "Run All Tests" to begin.
          </p>
        ) : (
          <pre className="text-sm whitespace-pre-wrap">
            {liveLogs.map((log, i) => (
              <div key={i}>👉 {log}</div>
            ))}
          </pre>
        )}
      </div>
    </div>
  );
};

export default RunTab;
