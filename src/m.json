{"folderInfo": {"path": ["Manage Service"], "folderId": 89231}, "testIssues": [{"id": 9202743, "key": "QE-5038", "summary": "OAUS-3312: Manage MSISDNs with Restricted Access to Profile", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9202751, "key": "QE-5046", "summary": "OAUS-3313: Set MSISDNs with Restricted Access to Profile while Connecting a Customer Number", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9202785, "key": "QE-5098", "summary": "OAUS-3326: Manage TV Permissions", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9777414, "key": "QE-7346", "summary": "QE-6151: Service notification order on Manage service notification", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9777422, "key": "QE-7347", "summary": "CM-3651: Fixline relocation: CM on Wishdate and Date of relocation", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "Eleni <PERSON>", "workflowStatusName": "To Do", "workflowStatusColor": "default", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9789052, "key": "QE-7378", "summary": "OAUS-4205: Changes on the HARD BUNDLE - HR - Phase-1", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9799943, "key": "QE-7383", "summary": "OAUS-4202: OneApp: in-app eSIM provisioning & installation", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9852460, "key": "QE-7527", "summary": "OAUS-4017: Rented & Purchased Devices", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9904448, "key": "QE-7648", "summary": "CM-4006: MultiSIM order summary: recurring vs onetime price", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9930936, "key": "QE-7661", "summary": "CM-3967: MultiSIM order: EID frontend validation", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 9971996, "key": "QE-7760", "summary": "OAUS-4221: Country Selection During Roaming Addon Booking", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10022054, "key": "QE-7815", "summary": "CM-3992: Transactions: B2B: Remove Contract Termination button for B2B End users", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10053926, "key": "QE-7907", "summary": "OAUS-4262: Add Hint for Customer Code in Service Connect Flow", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10076834, "key": "QE-7993", "summary": "OAUS-3976: Share Data: Data Transfer among 2 users", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10127053, "key": "QE-8179", "summary": "OAUS-4462: Add Hint for Cable Connect flow in Service Connect", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10190987, "key": "QE-8525", "summary": "OAUS-4344: Support for scale pricing in addon activation journey", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10338973, "key": "QE-8822", "summary": "OAUS-3660: OTT TV Standalone Assets", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10286607, "key": "QE-8667", "summary": "OAUS-4217: Connect service: B2B: Connect Services via Email and OTP", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10410795, "key": "QE-8968", "summary": "OAUS-3875 and OAUS-3874: Service State: Seasonal Service Suspension", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10427533, "key": "QE-8980", "summary": "Manage Service : Display HW Budget value", "description": "<p>SF Link: <a href=\"https://wiki.telekom.de/display/OABFF/Suggested+Manage+Services+flow?src=jira\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://wiki.telekom.de/display/OABFF/Suggested+Manage+Services+flow?src=jira</a></p>\n\n<p>Figma: <a href=\"https://www.figma.com/file/mt15BbNh4YvHubeMbUniEO/Rough-for-Hardware-Budget?type=design&amp;node-id=258-7839&amp;mode=design&amp;t=VamFiJyZuSjDpOki-0\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://www.figma.com/file/mt15BbNh4YvHubeMbUniEO/Rough-for-Hardware-Budget?type=design&amp;node-id=258-7839&amp;mode=design&amp;t=VamFiJyZuSjDpOki-0</a></p>", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10551182, "key": "QE-9132", "summary": "Roaming Cost Data Cap Addon", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10551829, "key": "QE-9135", "summary": "OAUS-4242 - SIM change guide", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Code Review (QA)", "workflowStatusColor": "inprogress", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10622871, "key": "QE-9270", "summary": "OAUS-4520 - Addon management: Legal Requirement for custom addons", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Code Review (QA)", "workflowStatusColor": "inprogress", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10624544, "key": "QE-9274", "summary": "OAUS-3703 - Risk Check in Hardware Budget Activation", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": ********, "key": "QE-9304", "summary": "Manage Service : [OAUS-4497] Adding/editing Account No on a prepaid service level", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": ********, "key": "QE-9305", "summary": "Manage Service : [OAUS-4496] Adding Account No in Prepaid Registration Process", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "To Do", "workflowStatusColor": "default", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": ********, "key": "QE-9370", "summary": "Manage Service Fixed: Link to Price Information about International Calls", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10717836, "key": "QE-9373", "summary": "Manage Service Mobile: Link to Price Information about Costs Abroad", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10907732, "key": "QE-10051", "summary": "[TestCases] Fixline relocation: Intro screen if user has no eligible services", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10930152, "key": "QE-10141", "summary": "[Test Cases] Display HW Budget value on Manage Service screen", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10934284, "key": "QE-10230", "summary": "[Test Case] Activate / Extend Hardware budget", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10937503, "key": "QE-10241", "summary": "[Test Case] Showing product summary documents", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10938162, "key": "QE-10242", "summary": "[Test Case] View Hardware budget", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "To Do", "workflowStatusColor": "default", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10937925, "key": "QE-10243", "summary": "OAUS-4240: UI merging of SIM list and SIM details screen", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "To Do", "workflowStatusColor": "default", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 10950445, "key": "QE-10275", "summary": "[Test Cases] Service State: Bundle Service Suspension / Cancel Suspension", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "Has the potential to affect progress.", "priorityIconUrl": "/images/icons/priorities/high.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11019259, "key": "QE-10394", "summary": "[Test Cases] Support Password method in new B2B Connect Service flow", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11042827, "key": "QE-10433", "summary": "[Test Cases] Mobile/Handy Insurance - Show in Assets", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11372080, "key": "QE-10978", "summary": "OAUS-4382: oneApp changes for Data-Privacy BFF: PIN, PUK", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "In Progress", "workflowStatusColor": "inprogress", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11372079, "key": "QE-10977", "summary": "OAUS-4832: Synchronized eSIM installation across all touchpoints", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "In Progress", "workflowStatusColor": "inprogress", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11445000, "key": "QE-11065", "summary": "OAUS 2296 : Activate physical SIM card", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11538752, "key": "QE-11231", "summary": "OAUS-4534: eSIM swap for another device: list of Telekom purchased devices", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "The operation or intended use of the Goods is possible only with major adverse effects. ", "priorityIconUrl": "/images/icons/priorities/major.svg", "assignee": "<PERSON><PERSON><PERSON><PERSON>", "workflowStatusName": "To Do", "workflowStatusColor": "default", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}, {"id": 11565586, "key": "QE-11300", "summary": "[TC] OneApp: Icons for device category not shown correctly", "description": "", "icon": "/download/resources/com.xpandit.plugins.xray/images/test.png", "priority": "All other Defects are class 3.", "priorityIconUrl": "/images/icons/priorities/minor.svg", "assignee": "<PERSON><PERSON><PERSON>", "workflowStatusName": "Done", "workflowStatusColor": "success", "labels": [], "components": [], "isInvalidIssue": false, "testType": "Manual", "invalidIssue": false}], "total": 42}