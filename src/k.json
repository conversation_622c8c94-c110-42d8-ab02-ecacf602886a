{"steps": [{"id": 3411531, "index": 1, "fields": {"Test Summary": {"type": "Data", "value": "N/A"}, "Action": {"type": "Wiki", "value": {"raw": "login -> click more -> settings", "rendered": "<p>login -&gt; click more -&gt; settings</p>"}}, "Data": {"type": "Wiki", "value": {"raw": "precondition:\n # The tab is only visible if more than one MSISDN (mobile postpaid or mobile prepaid services) with owner privileges are connected.\n # FF need to be enabled to show. (enableManageMsisdnWithRestrictedAccess)\n\nmock the profile call: [^profile for login.json]\n\n[https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira]\n\n[https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&node-id=1-8500&mode=design&t=xqHABEYlSWjTA4Lg-0]", "rendered": "<p>precondition:</p>\n<ol>\n<li>The tab is only visible if more than one MSISDN (mobile postpaid or mobile prepaid services) with owner privileges are connected.</li>\n<li>FF need to be enabled to show. (enableManageMsisdnWithRestrictedAccess)</li>\n</ol>\n\n\n<p>mock the profile call: <a href=\"https://jira.telekom.de/secure/attachment/3975854/3975854_profile+for+login.json\" title=\"profile for login.json\">profile for login.json</a></p>\n\n<p><a href=\"https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira</a></p>\n\n<p><a href=\"https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0</a></p>"}}, "Expected Result": {"type": "Wiki", "value": {"raw": "1) Access management tab should be visible if more than one MSISDN connected with owner privileges.\n\n2) Access management tab should not be visible if more less than one MSISDN connected with owner privileges.", "rendered": "<p>1) Access management tab should be visible if more than one MSISDN connected with owner privileges.</p>\n\n<p>2) Access management tab should not be visible if more less than one MSISDN connected with owner privileges.</p>"}}, "Natco": {"type": "Data", "value": ""}, "Execution Type": {"type": "Option", "value": []}}, "attachments": [{"id": 953844, "fileName": "Screenshot 2023-12-01 at 5.36.08 PM.png", "fileIcon": "image.gif", "mimeType": "image/png", "fileIconAlt": "PNG File", "fileSize": "474 kB", "numericalFileSize": 485000, "created": "2023-12-01T13:07:05+01:00", "createdDate": 1701432425424, "authorFullName": "", "fileURL": "https://jira.telekom.de/plugins/servlet/raven/attachment/953844/Screenshot+2023-12-01+at*****.08+PM.png", "filePath": "/QE/10000/QE-5038/xray_953844"}], "testCallStep": false}, {"id": 3411529, "index": 2, "fields": {"Test Summary": {"type": "Data", "value": ""}, "Action": {"type": "Wiki", "value": {"raw": "login -> click more -> settings -> access management", "rendered": "<p>login -&gt; click more -&gt; settings -&gt; access management</p>"}}, "Data": {"type": "Wiki", "value": {"raw": "User should have the more than one MSIDNs and user should have login with one of those.\n\nmock data:\n\n[^role and permision.json]\n\nhttps://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\n\nhttps://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&node-id=1-8500&mode=design&t=xqHABEYlSWjTA4Lg-0", "rendered": "<p>User should have the more than one MSIDNs and user should have login with one of those.</p>\n\n<p>mock data:</p>\n\n<p><a href=\"https://jira.telekom.de/secure/attachment/3975855/3975855_role+and+permision.json\" title=\"role and permision.json\">role and permision.json</a></p>\n\n<p><a href=\"https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira</a></p>\n\n<p><a href=\"https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0</a></p>"}}, "Expected Result": {"type": "Wiki", "value": {"raw": "Access management details page should appear.\n # There should be two section- *Numbers with full access* and *restricted numbers* (Text can be changes from CMS) if data is coming from BE - (/api/permissions/profile/5fba0646-f499-46b9-862e-6a09f2e39b42)\n # Toggle should be present for every MSISDNs.\n # Logged in text should visible if MSISDN that was used to log in to the current session.\n # if <PERSON><PERSON> fails, user should see error screen with retry button.", "rendered": "<p>Access management details page should appear.</p>\n<ol>\n<li>There should be two section- <b>Numbers with full access</b> and <b>restricted numbers</b> (Text can be changes from CMS) if data is coming from BE - (/api/permissions/profile/5fba0646-f499-46b9-862e-6a09f2e39b42)</li>\n<li>Toggle should be present for every MSISDNs.</li>\n<li>Logged in text should visible if MSISDN that was used to log in to the current session.</li>\n<li>if <PERSON><PERSON> fails, user should see error screen with retry button.</li>\n</ol>\n"}}, "Natco": {"type": "Data", "value": ""}, "Execution Type": {"type": "Option", "value": []}}, "attachments": [{"id": 953878, "fileName": "Screenshot 2023-11-30 at 4.53.10 PM.png", "fileIcon": "image.gif", "mimeType": "image/png", "fileIconAlt": "PNG File", "fileSize": "380 kB", "numericalFileSize": 389412, "created": "2023-12-01T13:32:34+01:00", "createdDate": 1701433954937, "authorFullName": "", "fileURL": "https://jira.telekom.de/plugins/servlet/raven/attachment/953878/Screenshot+2023-11-30+at*****.10+PM.png", "filePath": "/QE/10000/QE-5038/xray_953878"}], "testCallStep": false}, {"id": 3411528, "index": 3, "fields": {"Test Summary": {"type": "Data", "value": ""}, "Action": {"type": "Wiki", "value": {"raw": "login -> click more -> settings -> access management -> Switching the toggle", "rendered": "<p>login -&gt; click more -&gt; settings -&gt; access management -&gt; Switching the toggle</p>"}}, "Data": {"type": "Wiki", "value": {"raw": "User should have the more than one MSIDNs and user should have login with one of those.\n\nmock data:\n\n[^role and permision.json]\n\nhttps://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\n\nhttps://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&node-id=1-8500&mode=design&t=xqHABEYlSWjTA4Lg-0", "rendered": "<p>User should have the more than one MSIDNs and user should have login with one of those.</p>\n\n<p>mock data:</p>\n\n<p><a href=\"https://jira.telekom.de/secure/attachment/3975855/3975855_role+and+permision.json\" title=\"role and permision.json\">role and permision.json</a></p>\n\n<p><a href=\"https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira</a></p>\n\n<p><a href=\"https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0</a></p>"}}, "Expected Result": {"type": "Wiki", "value": {"raw": "# MSIDNs should move to restrict section if previously it has the full access.\n # MSIDNs should move to full access if previously it has not the full access.\n # If the changes are successful, the user should see a snackbar with the success message.\n # If the changes are successful, the user should see a snackbar with the UNDO and the toggle returns to the previous state.\n # If the changes are not successful, the user should see a snackbar with with the error, and the toggle returns to the previous state.", "rendered": "<ol>\n<li>MSIDNs should move to restrict section if previously it has the full access.</li>\n<li>MSIDNs should move to full access if previously it has not the full access.</li>\n<li>If the changes are successful, the user should see a snackbar with the success message.</li>\n<li>If the changes are successful, the user should see a snackbar with the UNDO and the toggle returns to the previous state.</li>\n<li>If the changes are not successful, the user should see a snackbar with with the error, and the toggle returns to the previous state.</li>\n</ol>\n"}}, "Natco": {"type": "Data", "value": ""}, "Execution Type": {"type": "Option", "value": []}}, "attachments": [{"id": 954062, "fileName": "Screenshot 2023-11-30 at 4.53.10 PM.png", "fileIcon": "image.gif", "mimeType": "image/png", "fileIconAlt": "PNG File", "fileSize": "380 kB", "numericalFileSize": 389412, "created": "2023-12-01T13:40:40+01:00", "createdDate": 1701434440291, "authorFullName": "", "fileURL": "https://jira.telekom.de/plugins/servlet/raven/attachment/954062/Screenshot+2023-11-30+at*****.10+PM.png", "filePath": "/QE/10000/QE-5038/xray_954062"}], "testCallStep": false}, {"id": 3411527, "index": 4, "fields": {"Test Summary": {"type": "Data", "value": ""}, "Action": {"type": "Wiki", "value": {"raw": "login -> click more -> settings -> access management -> Switching toggle for current session MSIDN", "rendered": "<p>login -&gt; click more -&gt; settings -&gt; access management -&gt; Switching toggle for current session MSIDN</p>"}}, "Data": {"type": "Wiki", "value": {"raw": "User should have the more than one MSIDNs and user should have login with one of those.\n\nmock data:\n\n[^role and permision.json]\n\nhttps://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\n\nhttps://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&node-id=1-8500&mode=design&t=xqHABEYlSWjTA4Lg-0", "rendered": "<p>User should have the more than one MSIDNs and user should have login with one of those.</p>\n\n<p>mock data:</p>\n\n<p><a href=\"https://jira.telekom.de/secure/attachment/3975855/3975855_role+and+permision.json\" title=\"role and permision.json\">role and permision.json</a></p>\n\n<p><a href=\"https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira</a></p>\n\n<p><a href=\"https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0</a></p>"}}, "Expected Result": {"type": "Wiki", "value": {"raw": "#  Two buttons should be displayed on popup: Cancel and Continue.\n # Tapping on continue, user should be immediately log out, and they will end up on the login screen.\n # Tapping on Cancel, popup should be dismiss .", "rendered": "<ol>\n<li> Two buttons should be displayed on popup: Cancel and Continue.</li>\n<li>Tapping on continue, user should be immediately log out, and they will end up on the login screen.</li>\n<li>Tapping on Cancel, popup should be dismiss .</li>\n</ol>\n"}}, "Natco": {"type": "Data", "value": ""}, "Execution Type": {"type": "Option", "value": []}}, "attachments": [{"id": 954215, "fileName": "Screenshot 2023-12-01 at 6.33.55 PM.png", "fileIcon": "image.gif", "mimeType": "image/png", "fileIconAlt": "PNG File", "fileSize": "365 kB", "numericalFileSize": 373594, "created": "2023-12-01T14:04:23+01:00", "createdDate": 1701435863015, "authorFullName": "", "fileURL": "https://jira.telekom.de/plugins/servlet/raven/attachment/954215/Screenshot+2023-12-01+at*****.55+PM.png", "filePath": "/QE/10000/QE-5038/xray_954215"}], "testCallStep": false}, {"id": 3411530, "index": 5, "fields": {"Test Summary": {"type": "Data", "value": ""}, "Action": {"type": "Wiki", "value": {"raw": "login -> click more -> settings -> access management -> Switching toggle for current session MSIDN", "rendered": "<p>login -&gt; click more -&gt; settings -&gt; access management -&gt; Switching toggle for current session MSIDN</p>"}}, "Data": {"type": "Wiki", "value": {"raw": "Only one MSIDN need to have in full access.\n\nmock data:\n\n[^role and permision.json]\n\n[https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira]\n\n[https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&node-id=1-8500&mode=design&t=xqHABEYlSWjTA4Lg-0]", "rendered": "<p>Only one MSIDN need to have in full access.</p>\n\n<p>mock data:</p>\n\n<p><a href=\"https://jira.telekom.de/secure/attachment/3975855/3975855_role+and+permision.json\" title=\"role and permision.json\">role and permision.json</a></p>\n\n<p><a href=\"https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://confluence.dtoneapp.telekom.net/display/OABFF/Suggested+Flow-Manage+MSISDNs+with+Restricted+Access+to+Profile?src=jira</a></p>\n\n<p><a href=\"https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0\" class=\"external-link\" target=\"_blank\" rel=\"nofollow noopener\">https://www.figma.com/file/AR149qCNefUAAPpRIZuv1b/OAUG-1513%2C1512---Set-MSISDNs-with-Restricted-Access-to-Profile-while-Connecting-a-Customer-Number?type=design&amp;node-id=1-8500&amp;mode=design&amp;t=xqHABEYlSWjTA4Lg-0</a></p>"}}, "Expected Result": {"type": "Wiki", "value": {"raw": "The toggle next to the last unrestricted MSISDN will be disabled.", "rendered": "<p>The toggle next to the last unrestricted MSISDN will be disabled.</p>"}}, "Natco": {"type": "Data", "value": ""}, "Execution Type": {"type": "Option", "value": []}}, "attachments": [{"id": 953361, "fileName": "Screenshot 2023-12-01 at 6.40.09 PM.png", "fileIcon": "image.gif", "mimeType": "image/png", "fileIconAlt": "PNG File", "fileSize": "348 kB", "numericalFileSize": 355912, "created": "2023-12-01T14:10:52+01:00", "createdDate": 1701436252044, "authorFullName": "", "fileURL": "https://jira.telekom.de/plugins/servlet/raven/attachment/953361/Screenshot+2023-12-01+at*****.09+PM.png", "filePath": "/QE/10000/QE-5038/xray_953361"}], "testCallStep": false}]}