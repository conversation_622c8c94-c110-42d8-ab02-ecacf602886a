package com.automation.tests;

import com.automation.core.TestBase;
import com.automation.config.ConfigManager;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.assertions.PlaywrightAssertions;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Regression Test Suite
 * Contains comprehensive regression tests for core functionality
 */
public class RegressionTests extends TestBase {
    
    @Test(description = "Comprehensive page load and navigation test", priority = 1, groups = {"regression"})
    public void testCompletePageNavigation() {
        logTestInfo("Starting comprehensive page navigation test");
        
        Page page = getPage();
        String baseUrl = ConfigManager.getBaseUrl();
        
        // Navigate to base URL
        navigateTo(baseUrl);
        
        // Verify page loads completely
        page.waitForLoadState(Page.LoadState.NETWORKIDLE);
        
        // Check page title
        String title = page.title();
        Assert.assertFalse(title.isEmpty(), "Page title should not be empty");
        logTestPass("Page title verified: " + title);
        
        // Check for common page elements
        PlaywrightAssertions.assertThat(page.locator("body")).isVisible();
        logTestPass("Page body is visible");
        
        // Check for any JavaScript errors
        page.onConsoleMessage(msg -> {
            if ("error".equals(msg.type())) {
                logTestWarning("Console error detected: " + msg.text());
            }
        });
        
        // Test page responsiveness
        testPageResponsiveness(page);
        
        takeScreenshot("Complete page navigation test");
        logTestPass("Comprehensive page navigation test completed successfully");
    }
    
    @Test(description = "Form interaction and validation test", priority = 2, groups = {"regression"})
    public void testFormInteractions() {
        logTestInfo("Starting form interaction test");
        
        Page page = getPage();
        navigateTo(ConfigManager.getBaseUrl());
        
        // Look for forms on the page
        int formCount = (int) page.locator("form").count();
        logTestInfo("Found " + formCount + " forms on the page");
        
        if (formCount > 0) {
            // Test first form if available
            testFormInteraction(page);
        } else {
            // Create a test form scenario
            logTestInfo("No forms found, testing input elements directly");
            testInputElements(page);
        }
        
        takeScreenshot("Form interaction test");
        logTestPass("Form interaction test completed successfully");
    }
    
    @Test(description = "Link validation and navigation test", priority = 3, groups = {"regression"})
    public void testLinkValidation() {
        logTestInfo("Starting link validation test");
        
        Page page = getPage();
        navigateTo(ConfigManager.getBaseUrl());
        
        // Get all links on the page
        int linkCount = (int) page.locator("a[href]").count();
        logTestInfo("Found " + linkCount + " links on the page");
        
        if (linkCount > 0) {
            // Test first few links
            int linksToTest = Math.min(linkCount, 5);
            
            for (int i = 0; i < linksToTest; i++) {
                try {
                    String href = page.locator("a[href]").nth(i).getAttribute("href");
                    String linkText = page.locator("a[href]").nth(i).textContent();
                    
                    if (href != null && !href.isEmpty()) {
                        logTestInfo("Testing link " + (i + 1) + ": " + linkText + " -> " + href);
                        
                        // Check if link is visible and clickable
                        PlaywrightAssertions.assertThat(page.locator("a[href]").nth(i)).isVisible();
                        
                        // For external links, just verify they're properly formatted
                        if (href.startsWith("http")) {
                            Assert.assertTrue(href.startsWith("http://") || href.startsWith("https://"),
                                "External link should have proper protocol: " + href);
                            logTestPass("External link format validated: " + href);
                        }
                    }
                } catch (Exception e) {
                    logTestWarning("Could not test link " + (i + 1) + ": " + e.getMessage());
                }
            }
        }
        
        takeScreenshot("Link validation test");
        logTestPass("Link validation test completed successfully");
    }
    
    @Test(description = "Cross-browser compatibility test", priority = 4, groups = {"regression"})
    public void testCrossBrowserCompatibility() {
        logTestInfo("Starting cross-browser compatibility test");
        
        Page page = getPage();
        navigateTo(ConfigManager.getBaseUrl());
        
        // Get browser information
        String userAgent = (String) page.evaluate("navigator.userAgent");
        logTestInfo("Testing with User Agent: " + userAgent);
        
        // Test JavaScript functionality
        Object jsResult = page.evaluate("typeof document !== 'undefined'");
        Assert.assertEquals(jsResult, true, "Document object should be available");
        logTestPass("JavaScript execution verified");
        
        // Test CSS support
        String bodyDisplay = (String) page.evaluate("getComputedStyle(document.body).display");
        Assert.assertNotNull(bodyDisplay, "CSS should be applied to body element");
        logTestPass("CSS rendering verified");
        
        // Test local storage
        boolean hasLocalStorage = (Boolean) page.evaluate("typeof(Storage) !== 'undefined'");
        logTestInfo("Local Storage support: " + hasLocalStorage);
        
        // Test viewport handling
        page.setViewportSize(1024, 768);
        page.waitForTimeout(1000);
        PlaywrightAssertions.assertThat(page.locator("body")).isVisible();
        logTestPass("Viewport resize handled correctly");
        
        takeScreenshot("Cross-browser compatibility test");
        logTestPass("Cross-browser compatibility test completed successfully");
    }
    
    @Test(description = "Performance and load time test", priority = 5, groups = {"regression"})
    public void testPerformanceAndLoadTime() {
        logTestInfo("Starting performance and load time test");
        
        Page page = getPage();
        
        // Measure page load time
        long startTime = System.currentTimeMillis();
        navigateTo(ConfigManager.getBaseUrl());
        page.waitForLoadState(Page.LoadState.NETWORKIDLE);
        long loadTime = System.currentTimeMillis() - startTime;
        
        logTestInfo("Page load time: " + loadTime + " ms");
        
        // Performance assertions
        long maxLoadTime = ConfigManager.getLongProperty("performance.threshold.page.load", 10000);
        Assert.assertTrue(loadTime < maxLoadTime, 
            "Page should load within " + maxLoadTime + " ms. Actual: " + loadTime + " ms");
        logTestPass("Page load time is within acceptable limits");
        
        // Test resource loading
        testResourceLoading(page);
        
        // Test memory usage (basic check)
        long memoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        // Perform some operations
        for (int i = 0; i < 5; i++) {
            page.evaluate("document.body.scrollTop = " + (i * 100));
            page.waitForTimeout(100);
        }
        
        long memoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long memoryUsed = memoryAfter - memoryBefore;
        
        logTestInfo("Memory used during test: " + memoryUsed + " bytes");
        
        takeScreenshot("Performance test");
        logTestPass("Performance and load time test completed successfully");
    }
    
    /**
     * Test page responsiveness across different viewport sizes
     */
    private void testPageResponsiveness(Page page) {
        logTestInfo("Testing page responsiveness");
        
        int[][] viewports = {
            {1920, 1080}, // Desktop
            {1366, 768},  // Laptop
            {768, 1024},  // Tablet
            {375, 667}    // Mobile
        };
        
        for (int[] viewport : viewports) {
            int width = viewport[0];
            int height = viewport[1];
            
            page.setViewportSize(width, height);
            page.waitForTimeout(500);
            
            // Verify page is still functional
            PlaywrightAssertions.assertThat(page.locator("body")).isVisible();
            logTestInfo("Responsive test passed for " + width + "x" + height);
        }
        
        // Reset to default
        page.setViewportSize(1920, 1080);
    }
    
    /**
     * Test form interaction
     */
    private void testFormInteraction(Page page) {
        logTestInfo("Testing form interaction");
        
        // Get first form
        if (page.locator("form").count() > 0) {
            // Look for input fields in the form
            int inputCount = (int) page.locator("form input").count();
            logTestInfo("Found " + inputCount + " input fields in form");
            
            // Test text inputs
            for (int i = 0; i < Math.min(inputCount, 3); i++) {
                try {
                    String inputType = page.locator("form input").nth(i).getAttribute("type");
                    if ("text".equals(inputType) || "email".equals(inputType) || inputType == null) {
                        page.locator("form input").nth(i).fill("test value " + i);
                        logTestInfo("Filled input field " + i);
                    }
                } catch (Exception e) {
                    logTestWarning("Could not fill input field " + i + ": " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Test input elements
     */
    private void testInputElements(Page page) {
        logTestInfo("Testing input elements");
        
        int inputCount = (int) page.locator("input").count();
        logTestInfo("Found " + inputCount + " input elements");
        
        // Test first few inputs
        for (int i = 0; i < Math.min(inputCount, 3); i++) {
            try {
                String inputType = page.locator("input").nth(i).getAttribute("type");
                if ("text".equals(inputType) || "email".equals(inputType) || inputType == null) {
                    if (page.locator("input").nth(i).isVisible()) {
                        page.locator("input").nth(i).fill("test input " + i);
                        logTestInfo("Successfully filled input element " + i);
                    }
                }
            } catch (Exception e) {
                logTestWarning("Could not interact with input element " + i + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Test resource loading
     */
    private void testResourceLoading(Page page) {
        logTestInfo("Testing resource loading");
        
        // Check for images
        int imageCount = (int) page.locator("img").count();
        logTestInfo("Found " + imageCount + " images on page");
        
        // Check for CSS files
        int cssCount = (int) page.locator("link[rel='stylesheet']").count();
        logTestInfo("Found " + cssCount + " CSS files");
        
        // Check for JavaScript files
        int jsCount = (int) page.locator("script[src]").count();
        logTestInfo("Found " + jsCount + " JavaScript files");
        
        logTestPass("Resource loading analysis completed");
    }
}
