package com.automation.tests;

import com.automation.core.TestBase;
import com.automation.config.ConfigManager;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * API Test Suite
 * Contains API testing examples and utilities
 */
public class APITests extends TestBase {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Test(description = "Test basic API connectivity", priority = 1, groups = {"api"})
    public void testAPIConnectivity() {
        logTestInfo("Testing basic API connectivity");
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            // Test with a public API
            String apiUrl = "https://httpbin.org/get";
            HttpGet request = new HttpGet(apiUrl);
            
            logTestInfo("Making GET request to: " + apiUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logTestInfo("Response status code: " + statusCode);
                logTestInfo("Response body length: " + responseBody.length());
                
                // Verify status code
                Assert.assertEquals(statusCode, 200, "API should return 200 status code");
                logTestPass("API connectivity test passed");
                
                // Verify response is valid JSON
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                Assert.assertNotNull(jsonResponse, "Response should be valid JSON");
                logTestPass("Response is valid JSON");
                
            }
            
        } catch (Exception e) {
            logTestFail("API connectivity test failed: " + e.getMessage());
            throw new RuntimeException("API test failed", e);
        }
    }
    
    @Test(description = "Test API response time", priority = 2, groups = {"api"})
    public void testAPIResponseTime() {
        logTestInfo("Testing API response time");
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            String apiUrl = "https://httpbin.org/delay/1";
            HttpGet request = new HttpGet(apiUrl);
            
            long startTime = System.currentTimeMillis();
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                long responseTime = System.currentTimeMillis() - startTime;
                
                logTestInfo("API response time: " + responseTime + " ms");
                
                // Verify response time is reasonable (less than 5 seconds)
                Assert.assertTrue(responseTime < 5000, 
                    "API response time should be less than 5 seconds. Actual: " + responseTime + " ms");
                
                logTestPass("API response time test passed: " + responseTime + " ms");
            }
            
        } catch (Exception e) {
            logTestFail("API response time test failed: " + e.getMessage());
            throw new RuntimeException("API response time test failed", e);
        }
    }
    
    @Test(description = "Test API POST request", priority = 3, groups = {"api"})
    public void testAPIPOSTRequest() {
        logTestInfo("Testing API POST request");
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            String apiUrl = "https://httpbin.org/post";
            HttpPost request = new HttpPost(apiUrl);
            
            // Add form parameters
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("name", "Test User"));
            params.add(new BasicNameValuePair("email", "<EMAIL>"));
            
            request.setEntity(new UrlEncodedFormEntity(params));
            
            logTestInfo("Making POST request to: " + apiUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logTestInfo("POST response status code: " + statusCode);
                
                // Verify status code
                Assert.assertEquals(statusCode, 200, "POST request should return 200 status code");
                
                // Verify response contains our data
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                JsonNode formData = jsonResponse.get("form");
                
                Assert.assertNotNull(formData, "Response should contain form data");
                Assert.assertEquals(formData.get("name").asText(), "Test User", "Name should match");
                Assert.assertEquals(formData.get("email").asText(), "<EMAIL>", "Email should match");
                
                logTestPass("API POST request test passed");
            }
            
        } catch (Exception e) {
            logTestFail("API POST request test failed: " + e.getMessage());
            throw new RuntimeException("API POST test failed", e);
        }
    }
    
    @Test(description = "Test API error handling", priority = 4, groups = {"api"})
    public void testAPIErrorHandling() {
        logTestInfo("Testing API error handling");
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            // Test 404 error
            String apiUrl = "https://httpbin.org/status/404";
            HttpGet request = new HttpGet(apiUrl);
            
            logTestInfo("Making request to 404 endpoint: " + apiUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                
                logTestInfo("Error response status code: " + statusCode);
                
                // Verify we get the expected error status
                Assert.assertEquals(statusCode, 404, "Should return 404 status code");
                logTestPass("404 error handling test passed");
            }
            
            // Test 500 error
            apiUrl = "https://httpbin.org/status/500";
            request = new HttpGet(apiUrl);
            
            logTestInfo("Making request to 500 endpoint: " + apiUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                
                logTestInfo("Server error response status code: " + statusCode);
                
                // Verify we get the expected error status
                Assert.assertEquals(statusCode, 500, "Should return 500 status code");
                logTestPass("500 error handling test passed");
            }
            
        } catch (Exception e) {
            logTestFail("API error handling test failed: " + e.getMessage());
            throw new RuntimeException("API error handling test failed", e);
        }
    }
    
    @Test(description = "Test API headers", priority = 5, groups = {"api"})
    public void testAPIHeaders() {
        logTestInfo("Testing API headers");
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            String apiUrl = "https://httpbin.org/headers";
            HttpGet request = new HttpGet(apiUrl);
            
            // Add custom headers
            request.setHeader("User-Agent", "Playwright-Automation-Framework/1.0");
            request.setHeader("X-Test-Header", "test-value");
            
            logTestInfo("Making request with custom headers to: " + apiUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logTestInfo("Headers response status code: " + statusCode);
                
                // Verify status code
                Assert.assertEquals(statusCode, 200, "Headers request should return 200 status code");
                
                // Verify our custom headers are present in the response
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                JsonNode headers = jsonResponse.get("headers");
                
                Assert.assertNotNull(headers, "Response should contain headers");
                Assert.assertNotNull(headers.get("User-Agent"), "User-Agent header should be present");
                Assert.assertNotNull(headers.get("X-Test-Header"), "Custom header should be present");
                
                String userAgent = headers.get("User-Agent").asText();
                String customHeader = headers.get("X-Test-Header").asText();
                
                Assert.assertTrue(userAgent.contains("Playwright-Automation-Framework"), 
                    "User-Agent should contain framework name");
                Assert.assertEquals(customHeader, "test-value", "Custom header value should match");
                
                logTestPass("API headers test passed");
            }
            
        } catch (Exception e) {
            logTestFail("API headers test failed: " + e.getMessage());
            throw new RuntimeException("API headers test failed", e);
        }
    }
    
    @Test(description = "Test API with authentication", priority = 6, groups = {"api"})
    public void testAPIAuthentication() {
        logTestInfo("Testing API authentication");
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            // Test basic auth
            String apiUrl = "https://httpbin.org/basic-auth/user/pass";
            HttpGet request = new HttpGet(apiUrl);
            
            // Add basic auth header
            String auth = java.util.Base64.getEncoder().encodeToString("user:pass".getBytes());
            request.setHeader("Authorization", "Basic " + auth);
            
            logTestInfo("Making authenticated request to: " + apiUrl);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logTestInfo("Auth response status code: " + statusCode);
                
                // Verify successful authentication
                Assert.assertEquals(statusCode, 200, "Authenticated request should return 200 status code");
                
                // Verify response contains authentication info
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                Assert.assertTrue(jsonResponse.get("authenticated").asBoolean(), 
                    "Response should indicate successful authentication");
                Assert.assertEquals(jsonResponse.get("user").asText(), "user", 
                    "Response should contain correct username");
                
                logTestPass("API authentication test passed");
            }
            
        } catch (Exception e) {
            logTestFail("API authentication test failed: " + e.getMessage());
            throw new RuntimeException("API authentication test failed", e);
        }
    }
}
