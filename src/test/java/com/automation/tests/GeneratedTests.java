package com.automation.tests;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import com.automation.config.ConfigManager;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.assertions.PlaywrightAssertions;
import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.List;

/**
 * Generated Tests Suite
 * Contains tests generated from user prompts using AI
 */
public class GeneratedTests extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    
    @DataProvider(name = "testPrompts")
    public Object[][] getTestPrompts() {
        return new Object[][] {
            {"Navigate to Google and search for 'Playwright automation'", "https://www.google.com"},
            {"Go to GitHub and verify the homepage loads", "https://github.com"},
            {"Visit example.com and check the page title", "https://example.com"},
            {"Open DuckDuckGo and perform a search", "https://duckduckgo.com"}
        };
    }
    
    @Test(description = "Execute AI-generated test cases", dataProvider = "testPrompts", groups = {"generated"})
    public void executeGeneratedTest(String prompt, String targetUrl) {
        logTestInfo("Executing generated test for prompt: " + prompt);
        logTestInfo("Target URL: " + targetUrl);
        
        try {
            // Generate test cases from prompt
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, targetUrl);
            
            Assert.assertFalse(testCases.isEmpty(), "At least one test case should be generated");
            logTestPass("Generated " + testCases.size() + " test case(s) from prompt");
            
            // Execute each generated test case
            for (TestCase testCase : testCases) {
                executeTestCase(testCase);
            }
            
            logTestPass("All generated test cases executed successfully");
            
        } catch (Exception e) {
            logTestFail("Failed to execute generated test: " + e.getMessage());
            throw e;
        }
    }
    
    @Test(description = "Test basic navigation generation", priority = 1, groups = {"generated"})
    public void testBasicNavigationGeneration() {
        logTestInfo("Testing basic navigation test generation");
        
        String prompt = "Navigate to the homepage and verify it loads";
        String targetUrl = ConfigManager.getBaseUrl();
        
        List<TestCase> testCases = testGenerator.generateTestCases(prompt, targetUrl);
        
        Assert.assertFalse(testCases.isEmpty(), "Should generate at least one test case");
        
        TestCase testCase = testCases.get(0);
        Assert.assertNotNull(testCase.getName(), "Test case should have a name");
        Assert.assertNotNull(testCase.getSteps(), "Test case should have steps");
        Assert.assertFalse(testCase.getSteps().isEmpty(), "Test case should have at least one step");
        
        logTestPass("Basic navigation test generation successful");
        
        // Execute the generated test
        executeTestCase(testCase);
    }
    
    @Test(description = "Test form interaction generation", priority = 2, groups = {"generated"})
    public void testFormInteractionGeneration() {
        logTestInfo("Testing form interaction test generation");
        
        String prompt = "Fill out a contact form with name 'John Doe' and email '<EMAIL>'";
        String targetUrl = "https://example.com";
        
        List<TestCase> testCases = testGenerator.generateTestCases(prompt, targetUrl);
        
        Assert.assertFalse(testCases.isEmpty(), "Should generate at least one test case");
        
        TestCase testCase = testCases.get(0);
        
        // Verify test case has input steps
        boolean hasTypeSteps = testCase.getSteps().stream()
                .anyMatch(step -> "type".equalsIgnoreCase(step.getAction()));
        
        Assert.assertTrue(hasTypeSteps, "Generated test should include type/input steps");
        logTestPass("Form interaction test generation successful");
        
        // Log the generated steps
        for (TestStep step : testCase.getSteps()) {
            logTestInfo("Generated step: " + step.getAction() + " - " + step.getDescription());
        }
    }
    
    @Test(description = "Test search functionality generation", priority = 3, groups = {"generated"})
    public void testSearchFunctionalityGeneration() {
        logTestInfo("Testing search functionality test generation");
        
        String prompt = "Search for 'automation testing' and verify results are displayed";
        String targetUrl = "https://www.google.com";
        
        List<TestCase> testCases = testGenerator.generateTestCases(prompt, targetUrl);
        
        Assert.assertFalse(testCases.isEmpty(), "Should generate at least one test case");
        
        TestCase testCase = testCases.get(0);
        
        // Verify test case has search-related steps
        boolean hasSearchSteps = testCase.getSteps().stream()
                .anyMatch(step -> step.getDescription().toLowerCase().contains("search"));
        
        Assert.assertTrue(hasSearchSteps, "Generated test should include search-related steps");
        logTestPass("Search functionality test generation successful");
    }
    
    @Test(description = "Test verification generation", priority = 4, groups = {"generated"})
    public void testVerificationGeneration() {
        logTestInfo("Testing verification test generation");
        
        String prompt = "Verify that the page title contains 'Example' and the main heading is visible";
        String targetUrl = "https://example.com";
        
        List<TestCase> testCases = testGenerator.generateTestCases(prompt, targetUrl);
        
        Assert.assertFalse(testCases.isEmpty(), "Should generate at least one test case");
        
        TestCase testCase = testCases.get(0);
        
        // Verify test case has verification steps
        boolean hasVerifySteps = testCase.getSteps().stream()
                .anyMatch(step -> "verify".equalsIgnoreCase(step.getAction()));
        
        Assert.assertTrue(hasVerifySteps, "Generated test should include verification steps");
        logTestPass("Verification test generation successful");
    }
    
    /**
     * Execute a generated test case
     */
    private void executeTestCase(TestCase testCase) {
        logTestInfo("Executing test case: " + testCase.getName());
        
        Page page = getPage();
        
        try {
            testCase.markAsRunning();
            long startTime = System.currentTimeMillis();
            
            for (TestStep step : testCase.getSteps()) {
                executeTestStep(step, page);
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            testCase.markAsPassed(executionTime);
            
            logTestPass("Test case executed successfully: " + testCase.getName());
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - System.currentTimeMillis();
            testCase.markAsFailed(e.getMessage(), executionTime);
            
            logTestFail("Test case failed: " + testCase.getName() + " - " + e.getMessage());
            throw new RuntimeException("Test case execution failed", e);
        }
    }
    
    /**
     * Execute a single test step
     */
    private void executeTestStep(TestStep step, Page page) {
        logTestInfo("Executing step: " + step.getDescription());
        
        try {
            step.markAsRunning();
            long startTime = System.currentTimeMillis();
            
            switch (step.getAction().toLowerCase()) {
                case "navigate":
                    page.navigate(step.getTarget());
                    logTestInfo("Navigated to: " + step.getTarget());
                    break;
                    
                case "click":
                    page.click(step.getTarget());
                    logTestInfo("Clicked: " + step.getTarget());
                    break;
                    
                case "type":
                case "fill":
                    page.fill(step.getTarget(), step.getValue());
                    logTestInfo("Typed '" + step.getValue() + "' into: " + step.getTarget());
                    break;
                    
                case "verify":
                case "assert":
                    executeVerificationStep(step, page);
                    break;
                    
                case "wait":
                    long waitTime = step.getTimeout() != null ? step.getTimeout() : 1000;
                    page.waitForTimeout((int) waitTime);
                    logTestInfo("Waited for: " + waitTime + " ms");
                    break;
                    
                default:
                    logTestWarning("Unknown action: " + step.getAction());
                    break;
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            step.markAsPassed(executionTime);
            
            // Take screenshot if required
            if (step.requiresScreenshot()) {
                takeScreenshot("Step_" + step.getStepNumber() + "_" + step.getAction());
            }
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - System.currentTimeMillis();
            step.markAsFailed(e.getMessage(), executionTime);
            
            logTestFail("Step failed: " + step.getDescription() + " - " + e.getMessage());
            
            // Take failure screenshot
            takeScreenshot("FAILED_Step_" + step.getStepNumber());
            
            throw e;
        }
    }
    
    /**
     * Execute verification step
     */
    private void executeVerificationStep(TestStep step, Page page) {
        String target = step.getTarget();
        String expectedValue = step.getValue();
        
        if ("visible".equalsIgnoreCase(expectedValue)) {
            PlaywrightAssertions.assertThat(page.locator(target)).isVisible();
            logTestInfo("Verified element is visible: " + target);
            
        } else if ("hidden".equalsIgnoreCase(expectedValue)) {
            PlaywrightAssertions.assertThat(page.locator(target)).isHidden();
            logTestInfo("Verified element is hidden: " + target);
            
        } else if (expectedValue != null && !expectedValue.isEmpty()) {
            // Text content verification
            String actualText = page.locator(target).textContent();
            step.setActualValue(actualText);
            
            if (actualText != null && actualText.contains(expectedValue)) {
                logTestInfo("Verified text content contains '" + expectedValue + "' in: " + target);
            } else {
                throw new AssertionError("Expected text '" + expectedValue + "' not found. Actual: " + actualText);
            }
            
        } else {
            // Default visibility check
            PlaywrightAssertions.assertThat(page.locator(target)).isVisible();
            logTestInfo("Verified element exists and is visible: " + target);
        }
    }
}
