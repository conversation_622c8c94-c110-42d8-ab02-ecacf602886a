import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { setTask, runTestRequest } from "../store/actions";
import "./AgentRunner.css"; // Import the CSS file

const AgentRunner = () => {
  const dispatch = useDispatch();
  const { task, taskResult, loading, error } = useSelector((state) => state);

  const handleRun = () => {
    if (task.trim()) {
      dispatch(runTestRequest(task));
    }
  };

  return (
    <div className="agent-runner-container">
      <h2 className="agent-title">🧠 Run Agent</h2>

      <textarea
        className="agent-textarea"
        rows={5}
        placeholder="Describe your task..."
        value={task}
        onChange={(e) => dispatch(setTask(e.target.value))}
      />

      <button className={`activeTab`} onClick={handleRun} disabled={loading}>
        {loading ? "Running..." : "Run Agent"}
      </button>

      {taskResult && (
        <div className="agent-output">
          <strong>Agent Response:</strong>
          <pre className="agent-pre">{taskResult}</pre>
        </div>
      )}

      {error && (
        <div className="agent-error">
          <strong>Error:</strong> {error}
        </div>
      )}
    </div>
  );
};

export default AgentRunner;
