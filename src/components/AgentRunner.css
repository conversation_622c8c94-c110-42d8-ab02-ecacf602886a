.agent-runner-container {
    max-width: 700px;
    margin: 40px auto;
    padding: 20px;
    font-family: Arial, sans-serif;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ddd;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  }
  
  .agent-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: black;
  }
  
  .agent-textarea {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    border-radius: 6px;
    border: 1px solid #ccc;
    resize: vertical;
    margin-bottom: 16px;
    box-sizing: border-box;
  }
  
  .agent-button {
    background-color: #007bff;
    color: #fff;
    padding: 10px 18px;
    font-size: 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .agent-button:disabled {
    background-color: #5a9bd4;
    cursor: not-allowed;
  }
  
  .agent-button:hover:not(:disabled) {
    background-color: #0056b3;
  }
  
  .agent-output {
    margin-top: 24px;
    padding: 16px;
    background-color: #f9f9f9;
    border-left: 4px solid #007bff;
    border-radius: 4px;
  }
  
  .agent-pre {
    white-space: pre-wrap;
    margin-top: 8px;
    font-family: monospace;
  }
  
  .agent-error {
    margin-top: 16px;
    color: red;
    font-weight: bold;
  }
  