import { createReducer } from "@reduxjs/toolkit";
import { setTask } from "./actions";
import {
  fetchHistoryRequest,
  fetchHistorySuccess,
  fetchHistoryFailure,
  runTestRequest,
  runTestSuccess,
  runTestFailure,
  fetchReportRequest,
  fetchReportSuccess,
  fetchReportFailure,
  abortTestRequest,
  abortTestSuccess,
  abortTestFailure,
  setLiveLogs,
  setHistory,
  setTestRunning,
  setActiveTab,
  setHtmlReport,
  fetchTestStepsRequest,
  fetchTestStepsSuccess,
  fetchTestStepsFailure,
} from "./actions";

const initialState = {
  task: "",
  activeTab: "execution",
  testCases: [],
  loading: false,
  history: [],
  htmlReport: "",
  liveLogs: [],
  testRunning: false,
  error: null,
  testResult:""
};

const rootReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(fetchHistoryRequest, (state) => {
      state.loading = true;
    })
    .addCase(fetchHistorySuccess, (state, action) => {
      state.loading = false;
      state.history = action.payload;
    })
    .addCase(fetchHistoryFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })
    .addCase(runTestRequest, (state) => {
      state.loading = true;
    })
    .addCase(runTestSuccess, (state, action) => {
      state.loading = false;
      state.testRunning = true;
      state.testResult = action.payload
    })
    .addCase(runTestFailure, (state, action) => {
      state.loading = false;
      state.testRunning = false;
      state.error = action.payload;
    })
    .addCase(fetchReportRequest, (state) => {
      state.loading = true;
    })
    .addCase(fetchReportSuccess, (state, action) => {
      state.loading = false;
      state.htmlReport = action.payload;
    })
    .addCase(fetchReportFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })
    .addCase(fetchTestStepsRequest, (state) => {
      state.loading = true;
    })
    .addCase(fetchTestStepsSuccess, (state, action) => {
      state.loading = false;
      state.testCases = action.payload;
    })
    .addCase(fetchTestStepsFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })
    .addCase(abortTestRequest, (state) => {
      state.loading = true;
    })
    .addCase(abortTestSuccess, (state) => {
      state.loading = false;
      state.testRunning = false;
      state.liveLogs.push("Test Aborted");
    })
    .addCase(abortTestFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })
    .addCase(setHistory, (state, action) => {
      state.history = [action.payload, ...state.history.slice(0, 19)];
    })
    .addCase(setLiveLogs, (state, action) => {
      state.liveLogs = [...action.payload];
    })
    .addCase(setTestRunning, (state, action) => {
      state.testRunning = action.payload;
    })
    .addCase(setActiveTab, (state, action) => {
      state.activeTab = action.payload;
    })
    .addCase(setHtmlReport, (state, action) => {
      state.htmlReport = action.payload;
    })
    .addCase(setTask, (state, action) => {
      state.task = action.payload;
    });
});

export default rootReducer;
