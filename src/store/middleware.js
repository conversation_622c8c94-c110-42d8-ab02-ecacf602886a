import {
  fetchHistoryRequest,
  fetchHistorySuccess,
  fetchHistoryFailure,
  runTestRequest,
  runTestSuccess,
  runTestFailure,
  fetchReportRequest,
  fetchReportSuccess,
  fetchReportFailure,
  abortTestRequest,
  abortTestSuccess,
  abortTestFailure,
  fetchTestStepsRequest,
  fetchTestStepsSuccess,
  fetchTestStepsFailure,
} from "./actions";

const apiMiddleware =
  ({ dispatch }) =>
  (next) =>
  async (action) => {
    next(action);

    switch (action.type) {
      case fetchHistoryRequest.type:
        try {
          const res = await fetch(
            "http://localhost:9191/api/test-python-history"
          );
          const data = await res.json();
          dispatch(fetchHistorySuccess(data.slice(0, 20)));
        } catch (err) {
          dispatch(fetchHistoryFailure(err.toString()));
        }
        break;

      case fetchTestStepsRequest.type:
        try {
          const res = await fetch(
            "http://localhost:9191/api/jira/issue/MMKC2-12612"
          );
          const data = await res.json();
          const testSteps = data
          dispatch(fetchTestStepsSuccess(testSteps));
        } catch (err) {
          dispatch(fetchTestStepsFailure(err.toString()));
        }
        break;

      case runTestRequest.type:
        try {
          const res = await fetch(
            "http://localhost:9191/api/run-python-tests",
            {
              method: "POST",
              body: JSON.stringify({ task: action.payload })
            },
   
          );
          const data = await res.json();
          dispatch(runTestSuccess(data));
        } catch (err) {
          dispatch(runTestFailure(err.toString()));
        }
        break;

      case fetchReportRequest.type:
        try {
          const res = await fetch("http://localhost:9191/api/report");
          const html = await res.text();
          dispatch(fetchReportSuccess(html));
        } catch (err) {
          dispatch(fetchReportFailure(err.toString()));
        }
        break;

      case abortTestRequest.type:
        try {
          const res = await fetch(
            "http://localhost:9191/api/abort-python-tests",
            {
              method: "POST",
            }
          );
          if (res.ok) {
            dispatch(abortTestSuccess());
          } else {
            throw new Error("Failed to abort test");
          }
        } catch (err) {
          dispatch(abortTestFailure(err.toString()));
        }
        break;

      default:
        break;
    }
  };

export default apiMiddleware;
