import { createAction } from "@reduxjs/toolkit";

export const fetchHistoryRequest = createAction("fetchHistory/request");
export const fetchHistorySuccess = createAction("fetchHistory/success");
export const fetchHistoryFailure = createAction("fetchHistory/failure");

export const runTestRequest = createAction("runTest/request");
export const runTestSuccess = createAction("runTest/success");
export const runTestFailure = createAction("runTest/failure");

export const fetchReportRequest = createAction("fetchReport/request");
export const fetchReportSuccess = createAction("fetchReport/success");
export const fetchReportFailure = createAction("fetchReport/failure");

export const abortTestRequest = createAction("abortTest/request");
export const abortTestSuccess = createAction("abortTest/success");
export const abortTestFailure = createAction("abortTest/failure");

export const fetchTestStepsRequest = createAction("fetchTestSteps/request");
export const fetchTestStepsSuccess = createAction("fetchTestSteps/success");
export const fetchTestStepsFailure = createAction("fetchTestSteps/failure");

export const setHistory = createAction("history/set");

export const setLiveLogs = createAction("liveLogs/set");

export const setActiveTab = createAction("activeTab/set");

export const setTestRunning = createAction("testRunning/set");
export const setTask = createAction("task/set");

export const setHtmlReport = createAction("htmlReport/set");


