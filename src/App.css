body {
  margin-left: 20px;
  padding: 0;
  background-color: #0f172a;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #f1f5f9;
}

.App {
  padding: 30px;
  max-width: 1200px;
  margin: auto;
}

h1 {
  text-align: center;
  font-size: 32px;
  color: #f472b6;
  margin-bottom: 30px;
}

button {
  background-color: #1e293b;
  color: #f1f5f9;
  border: 1px solid #d946ef;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.activeTab.active {
  background: linear-gradient(to right, #d946ef, #d11aed);  /* Darker gradient when active */
}

button:hover {
  background-color: #d946ef;
  color: white;
}

button:disabled {
  background-color: #6b7280;
  border-color: #6b7280;
  cursor: not-allowed;
  color: #d1d5db;
}

ul {
  list-style: none;
  padding: 0;
}

li {
  background-color: #1e293b;
  margin: 12px 0;
  padding: 15px;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

li:hover {
  background-color: #334155;
}

.log-box {
  background-color: #0f172a;
  border: 1px solid #d946ef;
  border-radius: 8px;
  padding: 10px;
  margin-top: 10px;
  white-space: pre-wrap;
  color: #e2e8f0;
}

.log-line {
  padding: 2px 0;
}

.loader {
  border: 6px solid #cbd5e1;
  border-top: 6px solid #d946ef;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

pre {
  font-size: 14px;
  color: #f8fafc;
  background-color: #1e293b;
  padding: 12px;
  border-radius: 8px;
  overflow-x: auto;
}

.tab-buttons {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.chart-container {
  margin-top: 40px;
  background-color: #1e293b;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(217, 70, 239, 0.2);
}
li > div {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

li > div span.status {
  margin-right: auto;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 6px;
}

li > div span.status.passed {
  background-color: #14532d;
  color: #bbf7d0;
}

li > div span.status.failed {
  background-color: #7f1d1d;
  color: #fecaca;
}

li > div button {
  margin-left: auto;
}
.loader-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.run-tab {
  display: flex;
  justify-content: center;
  margin-top: 80px;
}

.active-tab {
  background-color: #d946ef;
  color: white;
  border-color: #d946ef;
}

.console-box {
  background-color: #0f172a;
  border: 1px solid #d946ef;
  border-radius: 10px;
  margin-top: 20px;
}

.report-wrapper {
  all: initial;
  font-family: Arial, sans-serif;
  background-color: white;
  color: black;
  padding: 20px;
  border-radius: 12px;
}

.report-section {
  background-color: white;
  padding: 1.5rem;               /* Tailwind p-6 = 24px = 1.5rem */
  border-radius: 1rem;           /* Tailwind rounded-2xl = 16px */
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
              0 10px 10px -5px rgba(0, 0, 0, 0.04); /* Tailwind shadow-xl */
  height: 70vh;
  overflow: auto;
  margin-top: 30px;
}

.custom-heading-report {
  font-size: 1.25rem;        /* text-xl = 20px */
  font-weight: 700;          /* font-bold */
  margin-bottom: 1rem;       /* mb-4 = 16px */
  color: #f9a8d4;            /* text-pink-300 */
}

.text-slate-400 {
  color: #94a3b8;
}

.custom-container-report {
  background-color: #334155;   /* bg-slate-700 */
  padding: 1rem;               /* p-4 = 16px */
  border-radius: 0.5rem;       /* rounded-lg = 8px */
  overflow: auto;
  min-height: 70vh;
}


/* General Button Styles */
.filter-btn {
  background-color: #1e293b;
  color: #f1f5f9;
  border: 1px solid #d946ef;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.filter-btn:hover {
  background-color: #d946ef;
  color: white;
}

/* Active Button Styles */
.filter-btn.active {
  background: linear-gradient(to right, #d946ef, #d11aed);  /* Darker gradient when active */
}

.filter-btn:focus {
  outline: none;  /* Remove outline */
}

.filter-buttons{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}






