const testCaseMock = [
    {
      id: 1,
      name: "Validate Header - MAVI",
      type: "MAVI",
      description:
        "TC_01: Checks whether the header 'Magenta<PERSON>iew' appears after login.",
      steps: [
        "Navigate to the MAVI URL",
        "Perform login if required",
        "Check if '<PERSON><PERSON><PERSON><PERSON>ie<PERSON>' is visible in the header",
      ],
      code: `
                @pytest.mark.asyncio
                async def test_MAVI_validate_header():
                    llm = ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=api_key)
                    browser = Browser(
                        config=BrowserConfig(
                            chrome_instance_path='/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                            disable_security=True
                        )
                    )
                    try:
                        agent = Agent(
                            task="""
                                ### Step 1: Navigate to the Website
                                - Open url: 'https://root-cit2.nonprod.mavi.yo-digital.com/#/'
                                - Wait for few seconds for screen to get completely loaded

                                ### Step 2: Skip login if user is already logged in
                                - If search bar is visible then move to next step
                                - Else
                                    - If Microsoft login window will open, then tap on 'use another account'
                                    - Else If 'More information' is visible then tap on 'different account' and on next screen tap on 'use another account'
                                    - Enter email - <EMAIL>
                                    - Click on next and wait till office 365 login page gets opened
                                    - Enter password as 'ikNpDfrOzy.59' and tap on sign in button
                                    - Now stay signed In screen will open, tap on no and continue
                                    - Wait till header 'MagentaView' is visible in screen along with search text

                                ### Step 3: Validate test result
                                - Validate the MagentaView text on search page to validate the page opened correctly.

                            """,
                            llm=llm,
                            browser=browser
                        )

                        result = await agent run()
                        print("Agent Result:", result)
                        result_str = str(result)
                        assert "MagentaView" in result_str, "Test failed: 'MagentaView' not found in the agent result."

                    finally:
                        os.system("pkill -f 'Google Chrome'")
                `,
    },
    {
      id: 2,
      name: "Search Field Validation - MAVI",
      type: "MAVI",
      description:
        "TC_02:Tests if customer number search works and returns IBAN.",
      steps: [
        "Login to the website",
        "Enter customer number '1234567890'",
        "Click search and verify 'IBAN' appears",
      ],
      code: `
                @pytest.mark.asyncio
                async def test_MAVI_validate_search():
                    llm = ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=api_key)
                    browser = Browser(config=BrowserConfig(
                        chrome_instance_path='/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                        disable_security=True
                    ))
                    try:
                        agent = Agent(
                            task=\"""
                                - Perform login
                                - Search for customer number
                                - Validate result contains 'IBAN'
                            \""",
                            llm=llm, browser=browser
                        )
                        result = await agent run()
                        assert "anzeigen" in str(result)
                    finally:
                        os.system("pkill -f 'Google Chrome'")
                `,
    },
    {
      id: 3,
      name: "Validate Profile Logged-in - MAVI",
      type: "MAVI",
      description:
        "TC_03: Verifies that user profile name is visible after login.",
      steps: [
        "Login to the website",
        "Open business user menu",
        "Validate presence of '9P700' or 'Vertriebsorganisation'",
      ],
      code: `
                @pytest.mark.asyncio
                async def test_MAVI_validate_profile_logged_in():
                    llm = ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=api_key)
                    browser = Browser(
                        config=BrowserConfig(
                            chrome_instance_path='/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                            disable_security=True
                        )
                    )
                    try:
                        agent = Agent(
                            task="""
                                ### Step 1: Navigate to the Website
                                - Open url: 'https://root-cit2.nonprod.mavi.yo-digital.com/#/'
                                - Wait for the page to load fully

                                ### Step 2: Skip login if user is already logged in
                                - If search bar is visible then move to next step
                                - Else
                                    - If Microsoft login window will open, then tap on 'use another account'
                                    - Else If 'More information' is visible then tap on 'different account' and on next screen tap on 'use another account'
                                    - Enter email - <EMAIL>
                                    - Click on next and wait till office 365 login page gets opened
                                    - Enter password as 'ikNpDfrOzy.59' and tap on sign in button
                                    - Now stay signed In screen will open, tap on no and continue
                                    - Wait till header 'MagentaView' is visible in screen along with search text

                                ### Step 3: Open business-user menu
                                - Find the business-user button and tap on it to open it

                                ### Step 4: Validate the user logged is in or not
                                - Check that the user profile is visible with name '9P700' or 'Vertriebsorganisation'

                            """,
                            llm=llm,
                            browser=browser
                        )

                        result = await agent run()
                        print("Agent Result:", result)
                        result_str = str(result)
                        assert result success is True, "Test failed: Agent did not complete the task successfully."
                        assert "9P700" in result_str, "Test failed: 'Nicht aufrufbar' not found in the agent result."

                    finally:
                        os.system("pkill -f 'Google Chrome'")
                `,
    },
    {
      id: 4,
      name: "Validate insurance visible",
      type: "MMKC",
      description: "TC_01: Test validate MMKC insurance visible.",
      steps: [
        "Open the Login Page",
        "Handle Login",
        "Validate Insurance Info'",
      ],
      code: `
                @pytest.mark.asyncio
                async def test_MAVI_validate_profile_logged_in():
                    llm = ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=api_key)
                    browser = Browser(
                        config=BrowserConfig(
                            chrome_instance_path='/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                            disable_security=True
                        )
                    )
                    try:
                        agent = Agent(
                            task="""
                                ### Step 1: Navigate to the Website
                                - Open url: 'https://root-cit2.nonprod.mavi.yo-digital.com/#/'
                                - Wait for the page to load fully

                                ### Step 2: Skip login if user is already logged in
                                - If search bar is visible then move to next step
                                - Else
                                    - If Microsoft login window will open, then tap on 'use another account'
                                    - Else If 'More information' is visible then tap on 'different account' and on next screen tap on 'use another account'
                                    - Enter email - <EMAIL>
                                    - Click on next and wait till office 365 login page gets opened
                                    - Enter password as 'ikNpDfrOzy.59' and tap on sign in button
                                    - Now stay signed In screen will open, tap on no and continue
                                    - Wait till header 'MagentaView' is visible in screen along with search text

                                ### Step 3: Open business-user menu
                                - Find the business-user button and tap on it to open it

                                ### Step 4: Validate the user logged is in or not
                                - Check that the user profile is visible with name '9P700' or 'Vertriebsorganisation'

                            """,
                            llm=llm,
                            browser=browser
                        )

                        result = await agent run()
                        print("Agent Result:", result)
                        result_str = str(result)
                        assert result success is True, "Test failed: Agent did not complete the task successfully."
                        assert "9P700" in result_str, "Test failed: 'Nicht aufrufbar' not found in the agent result."

                    finally:
                        os.system("pkill -f 'Google Chrome'")
                `,
    },
  ]
  export default testCaseMock;