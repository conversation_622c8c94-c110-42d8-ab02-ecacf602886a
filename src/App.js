import React, { useState } from "react";
import "./App.css";

import TestCasesTab from "./tabs/TestCasesTab";
import ExecutionTab from "./tabs/ExecutionTab";
import RunTab from "./tabs/RunTab";
import ReportTab from "./tabs/ReportTab";

import { useDispatch, useSelector } from "react-redux";
import { setActiveTab} from "./store/actions";

const App = () => {
 
 
 const activeTab = useSelector((state) => state.activeTab);
  const dispatch = useDispatch();
  const tabs = [
    { id: "test-cases", label: "🧪 Test Cases" },
    { id: "execution", label: "📜 Execution" },
    { id: "run", label: "⚡ Run" },
    { id: "report", label: "📊 Report" },
  ];
  return (
    <div className="min-h-screen bg-slate-900 text-slate-100 font-sans p-6 ml-8 relative">
      <div className="max-w-6xl mx-auto space-y-10">
        <h1 className="text-3xl font-bold text-center text-pink-400 tracking-wide">
          SEN AutoPilot
        </h1>
        {/* Tabs */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => dispatch(setActiveTab(tab.id))}
              className={`activeTab ${
                activeTab === tab.id
                  ? "active"
                  : ""
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
     
        {activeTab === "test-cases" && (
          <div className="tab-content">
            <TestCasesTab />
          </div>
        )}
        {activeTab === "execution" && (
          <div className="tab-content">
            <ExecutionTab />
          </div>
        )}
        {activeTab === "run" && (
          <div className="tab-content">
            <RunTab />
          </div>
        )}
        {activeTab === "report" && (
          <div className="tab-content">
            <ReportTab />
          </div>
        )}
       
      </div>
    </div>
  );
};

export default App;
