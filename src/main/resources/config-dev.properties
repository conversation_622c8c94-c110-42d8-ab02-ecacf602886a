# Development Environment Configuration
# Overrides default configuration for development environment

# Environment
environment=dev

# Application URLs
base.url=https://dev.example.com
api.base.url=https://dev-api.example.com

# Browser Configuration
headless=false
browser=chromium

# Debug Settings
log.level=DEBUG
log.console.enabled=true

# Screenshots
screenshot.on.failure=true
screenshot.on.success=true

# Video Recording
video.recording=true

# Performance (relaxed for dev)
performance.threshold.page.load=10000
performance.threshold.api.response=5000

# Parallel Execution (reduced for dev)
thread.count=2
parallel.tests=false

# Retry Configuration
retry.failed.tests=true
max.retry.count=3

# Test Data
test.data.path=src/test/resources/testdata/dev

# Database (dev database)
database.url=****************************************
database.username=dev_user
database.password=dev_password

# API Keys (dev keys)
api.key=dev_api_key_here
openai.api.key=dev_openai_key_here
augment.api.key=dev_augment_key_here

# Test Generation
test.generation.enabled=true
test.generation.model=gpt-3.5-turbo
test.generation.temperature=0.5

# Notifications (disabled for dev)
notifications.enabled=false

# Security (relaxed for dev)
security.testing=false
security.headers.check=false

# Accessibility
accessibility.testing=true
accessibility.standards=WCAG2A

# Mobile Testing
mobile.testing=false

# Cross-browser Testing
cross.browser.testing=false

# Performance Monitoring
performance.monitoring=true

# CI/CD
ci.environment=false
