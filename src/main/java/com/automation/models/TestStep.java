package com.automation.models;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * Test Step Model
 * Represents an individual step within a test case
 */
public class TestStep {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("stepNumber")
    private Integer stepNumber;
    
    @JsonProperty("action")
    private String action; // navigate, click, type, verify, wait, etc.
    
    @JsonProperty("target")
    private String target; // CSS selector, URL, element identifier
    
    @JsonProperty("value")
    private String value; // Input value, expected text, etc.
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("timeout")
    private Long timeout; // in milliseconds
    
    @JsonProperty("optional")
    private Boolean optional = false;
    
    @JsonProperty("retryOnFailure")
    private Boolean retryOnFailure = false;
    
    @JsonProperty("waitCondition")
    private String waitCondition; // visible, hidden, enabled, disabled, etc.
    
    @JsonProperty("screenshot")
    private Boolean screenshot = false;
    
    @JsonProperty("status")
    private String status; // PENDING, RUNNING, PASSED, FAILED, SKIPPED
    
    @JsonProperty("errorMessage")
    private String errorMessage;
    
    @JsonProperty("actualValue")
    private String actualValue; // For verification steps
    
    @JsonProperty("executionTime")
    private Long executionTime; // in milliseconds
    
    @JsonProperty("executedAt")
    private LocalDateTime executedAt;
    
    @JsonProperty("screenshotPath")
    private String screenshotPath;
    
    @JsonProperty("additionalData")
    private Map<String, Object> additionalData;
    
    // Default constructor
    public TestStep() {
        this.status = "PENDING";
        this.optional = false;
        this.retryOnFailure = false;
        this.screenshot = false;
    }
    
    // Constructor with basic fields
    public TestStep(String action, String target, String value, String description) {
        this();
        this.action = action;
        this.target = target;
        this.value = value;
        this.description = description;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public Integer getStepNumber() {
        return stepNumber;
    }
    
    public void setStepNumber(Integer stepNumber) {
        this.stepNumber = stepNumber;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public String getTarget() {
        return target;
    }
    
    public void setTarget(String target) {
        this.target = target;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Long getTimeout() {
        return timeout;
    }
    
    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }
    
    public Boolean getOptional() {
        return optional;
    }
    
    public void setOptional(Boolean optional) {
        this.optional = optional;
    }
    
    public Boolean getRetryOnFailure() {
        return retryOnFailure;
    }
    
    public void setRetryOnFailure(Boolean retryOnFailure) {
        this.retryOnFailure = retryOnFailure;
    }
    
    public String getWaitCondition() {
        return waitCondition;
    }
    
    public void setWaitCondition(String waitCondition) {
        this.waitCondition = waitCondition;
    }
    
    public Boolean getScreenshot() {
        return screenshot;
    }
    
    public void setScreenshot(Boolean screenshot) {
        this.screenshot = screenshot;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getActualValue() {
        return actualValue;
    }
    
    public void setActualValue(String actualValue) {
        this.actualValue = actualValue;
    }
    
    public Long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }
    
    public LocalDateTime getExecutedAt() {
        return executedAt;
    }
    
    public void setExecutedAt(LocalDateTime executedAt) {
        this.executedAt = executedAt;
    }
    
    public String getScreenshotPath() {
        return screenshotPath;
    }
    
    public void setScreenshotPath(String screenshotPath) {
        this.screenshotPath = screenshotPath;
    }
    
    public Map<String, Object> getAdditionalData() {
        return additionalData;
    }
    
    public void setAdditionalData(Map<String, Object> additionalData) {
        this.additionalData = additionalData;
    }
    
    // Utility methods
    public boolean isPassed() {
        return "PASSED".equals(status);
    }
    
    public boolean isFailed() {
        return "FAILED".equals(status);
    }
    
    public boolean isSkipped() {
        return "SKIPPED".equals(status);
    }
    
    public boolean isRunning() {
        return "RUNNING".equals(status);
    }
    
    public boolean isPending() {
        return "PENDING".equals(status);
    }
    
    public void markAsRunning() {
        this.status = "RUNNING";
        this.executedAt = LocalDateTime.now();
    }
    
    public void markAsPassed(long executionTime) {
        this.status = "PASSED";
        this.executionTime = executionTime;
    }
    
    public void markAsFailed(String errorMessage, long executionTime) {
        this.status = "FAILED";
        this.errorMessage = errorMessage;
        this.executionTime = executionTime;
    }
    
    public void markAsSkipped(String reason) {
        this.status = "SKIPPED";
        this.errorMessage = reason;
    }
    
    public boolean isNavigationStep() {
        return "navigate".equalsIgnoreCase(action);
    }
    
    public boolean isClickStep() {
        return "click".equalsIgnoreCase(action);
    }
    
    public boolean isTypeStep() {
        return "type".equalsIgnoreCase(action) || "fill".equalsIgnoreCase(action);
    }
    
    public boolean isVerificationStep() {
        return "verify".equalsIgnoreCase(action) || "assert".equalsIgnoreCase(action);
    }
    
    public boolean isWaitStep() {
        return "wait".equalsIgnoreCase(action);
    }
    
    public boolean requiresScreenshot() {
        return screenshot || isFailed() || isVerificationStep();
    }
    
    public boolean shouldRetryOnFailure() {
        return retryOnFailure && isFailed();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestStep testStep = (TestStep) o;
        return Objects.equals(id, testStep.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "TestStep{" +
                "stepNumber=" + stepNumber +
                ", action='" + action + '\'' +
                ", target='" + target + '\'' +
                ", value='" + value + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
    
    /**
     * Create a copy of this test step
     */
    public TestStep copy() {
        TestStep copy = new TestStep();
        copy.setAction(this.action);
        copy.setTarget(this.target);
        copy.setValue(this.value);
        copy.setDescription(this.description);
        copy.setTimeout(this.timeout);
        copy.setOptional(this.optional);
        copy.setRetryOnFailure(this.retryOnFailure);
        copy.setWaitCondition(this.waitCondition);
        copy.setScreenshot(this.screenshot);
        copy.setAdditionalData(this.additionalData);
        return copy;
    }
}
