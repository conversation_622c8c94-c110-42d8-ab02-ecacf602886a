package com.automation.models;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Test Case Model
 * Represents a complete test case with steps, metadata, and execution details
 */
public class TestCase {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("priority")
    private String priority; // HIGH, MEDIUM, LOW
    
    @JsonProperty("status")
    private String status; // CREATED, READY, RUNNING, PASSED, FAILED, SKIPPED
    
    @JsonProperty("steps")
    private List<TestStep> steps;
    
    @JsonProperty("targetUrl")
    private String targetUrl;
    
    @JsonProperty("originalPrompt")
    private String originalPrompt;
    
    @JsonProperty("testType")
    private String testType; // SMOKE, REGRESSION, FUNCTIONAL, API, etc.
    
    @JsonProperty("tags")
    private List<String> tags;
    
    @JsonProperty("expectedDuration")
    private Long expectedDuration; // in milliseconds
    
    @JsonProperty("actualDuration")
    private Long actualDuration; // in milliseconds
    
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @JsonProperty("executedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executedAt;
    
    @JsonProperty("createdBy")
    private String createdBy;
    
    @JsonProperty("environment")
    private String environment;
    
    @JsonProperty("browser")
    private String browser;
    
    @JsonProperty("errorMessage")
    private String errorMessage;
    
    @JsonProperty("screenshotPath")
    private String screenshotPath;
    
    @JsonProperty("videoPath")
    private String videoPath;
    
    @JsonProperty("retryCount")
    private Integer retryCount = 0;
    
    @JsonProperty("maxRetries")
    private Integer maxRetries = 3;
    
    // Default constructor
    public TestCase() {
        this.status = "CREATED";
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.retryCount = 0;
        this.maxRetries = 3;
    }
    
    // Constructor with basic fields
    public TestCase(String name, String description, String priority) {
        this();
        this.name = name;
        this.description = description;
        this.priority = priority;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }
    
    public List<TestStep> getSteps() {
        return steps;
    }
    
    public void setSteps(List<TestStep> steps) {
        this.steps = steps;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getTargetUrl() {
        return targetUrl;
    }
    
    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }
    
    public String getOriginalPrompt() {
        return originalPrompt;
    }
    
    public void setOriginalPrompt(String originalPrompt) {
        this.originalPrompt = originalPrompt;
    }
    
    public String getTestType() {
        return testType;
    }
    
    public void setTestType(String testType) {
        this.testType = testType;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public Long getExpectedDuration() {
        return expectedDuration;
    }
    
    public void setExpectedDuration(Long expectedDuration) {
        this.expectedDuration = expectedDuration;
    }
    
    public Long getActualDuration() {
        return actualDuration;
    }
    
    public void setActualDuration(Long actualDuration) {
        this.actualDuration = actualDuration;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public LocalDateTime getExecutedAt() {
        return executedAt;
    }
    
    public void setExecutedAt(LocalDateTime executedAt) {
        this.executedAt = executedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getEnvironment() {
        return environment;
    }
    
    public void setEnvironment(String environment) {
        this.environment = environment;
    }
    
    public String getBrowser() {
        return browser;
    }
    
    public void setBrowser(String browser) {
        this.browser = browser;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getScreenshotPath() {
        return screenshotPath;
    }
    
    public void setScreenshotPath(String screenshotPath) {
        this.screenshotPath = screenshotPath;
    }
    
    public String getVideoPath() {
        return videoPath;
    }
    
    public void setVideoPath(String videoPath) {
        this.videoPath = videoPath;
    }
    
    public Integer getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
    
    public Integer getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
    }
    
    // Utility methods
    public boolean isPassed() {
        return "PASSED".equals(status);
    }
    
    public boolean isFailed() {
        return "FAILED".equals(status);
    }
    
    public boolean isSkipped() {
        return "SKIPPED".equals(status);
    }
    
    public boolean isRunning() {
        return "RUNNING".equals(status);
    }
    
    public boolean canRetry() {
        return retryCount < maxRetries && isFailed();
    }
    
    public void incrementRetryCount() {
        this.retryCount++;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void markAsRunning() {
        this.status = "RUNNING";
        this.executedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public void markAsPassed(long duration) {
        this.status = "PASSED";
        this.actualDuration = duration;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void markAsFailed(String errorMessage, long duration) {
        this.status = "FAILED";
        this.errorMessage = errorMessage;
        this.actualDuration = duration;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void markAsSkipped(String reason) {
        this.status = "SKIPPED";
        this.errorMessage = reason;
        this.updatedAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestCase testCase = (TestCase) o;
        return Objects.equals(id, testCase.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "TestCase{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", status='" + status + '\'' +
                ", priority='" + priority + '\'' +
                ", stepsCount=" + (steps != null ? steps.size() : 0) +
                ", createdAt=" + createdAt +
                '}';
    }
}
