package com.automation.core;

import com.microsoft.playwright.*;
import com.automation.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * Driver Manager for Playwright Browser Management
 * Handles browser initialization, configuration, and cleanup
 */
public class DriverManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DriverManager.class);
    private static final ThreadLocal<Playwright> playwright = new ThreadLocal<>();
    private static final ThreadLocal<Browser> browser = new ThreadLocal<>();
    private static final ThreadLocal<BrowserContext> context = new ThreadLocal<>();
    private static final ThreadLocal<Page> page = new ThreadLocal<>();
    
    /**
     * Initialize Playwright and Browser
     */
    public static void initializeDriver() {
        try {
            // Create Playwright instance
            Playwright playwrightInstance = Playwright.create();
            playwright.set(playwrightInstance);
            
            // Get browser configuration
            String browserName = ConfigManager.getProperty("browser", "chromium");
            boolean headless = Boolean.parseBoolean(ConfigManager.getProperty("headless", "false"));
            
            // Launch browser
            Browser browserInstance = launchBrowser(playwrightInstance, browserName, headless);
            browser.set(browserInstance);
            
            // Create browser context
            BrowserContext contextInstance = createBrowserContext(browserInstance);
            context.set(contextInstance);
            
            // Create new page
            Page pageInstance = contextInstance.newPage();
            page.set(pageInstance);
            
            logger.info("Driver initialized successfully with browser: {}", browserName);
            
        } catch (Exception e) {
            logger.error("Failed to initialize driver: {}", e.getMessage(), e);
            throw new RuntimeException("Driver initialization failed", e);
        }
    }
    
    /**
     * Launch browser based on configuration
     */
    private static Browser launchBrowser(Playwright playwright, String browserName, boolean headless) {
        BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                .setHeadless(headless)
                .setSlowMo(50)
                .setArgs(Arrays.asList(
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--no-sandbox"
                ));
        
        switch (browserName.toLowerCase()) {
            case "firefox":
                return playwright.firefox().launch(launchOptions);
            case "webkit":
            case "safari":
                return playwright.webkit().launch(launchOptions);
            case "chromium":
            case "chrome":
            default:
                return playwright.chromium().launch(launchOptions);
        }
    }
    
    /**
     * Create browser context with configurations
     */
    private static BrowserContext createBrowserContext(Browser browser) {
        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setViewportSize(1920, 1080)
                .setLocale("en-US")
                .setTimezoneId("America/New_York")
                .setRecordVideoDir(Paths.get("test-results/videos"))
                .setRecordVideoSize(1920, 1080);
        
        // Add permissions if needed
        List<String> permissions = Arrays.asList("geolocation", "notifications");
        contextOptions.setPermissions(permissions);
        
        return browser.newContext(contextOptions);
    }
    
    /**
     * Get current Playwright instance
     */
    public static Playwright getPlaywright() {
        return playwright.get();
    }
    
    /**
     * Get current Browser instance
     */
    public static Browser getBrowser() {
        return browser.get();
    }
    
    /**
     * Get current BrowserContext instance
     */
    public static BrowserContext getContext() {
        return context.get();
    }
    
    /**
     * Get current Page instance
     */
    public static Page getPage() {
        return page.get();
    }
    
    /**
     * Create new page in current context
     */
    public static Page createNewPage() {
        BrowserContext currentContext = getContext();
        if (currentContext != null) {
            Page newPage = currentContext.newPage();
            page.set(newPage);
            return newPage;
        }
        throw new RuntimeException("Browser context not initialized");
    }
    
    /**
     * Navigate to URL
     */
    public static void navigateTo(String url) {
        Page currentPage = getPage();
        if (currentPage != null) {
            currentPage.navigate(url);
            logger.info("Navigated to: {}", url);
        } else {
            throw new RuntimeException("Page not initialized");
        }
    }
    
    /**
     * Take screenshot
     */
    public static byte[] takeScreenshot() {
        Page currentPage = getPage();
        if (currentPage != null) {
            return currentPage.screenshot();
        }
        return null;
    }
    
    /**
     * Take screenshot and save to file
     */
    public static String takeScreenshot(String fileName) {
        Page currentPage = getPage();
        if (currentPage != null) {
            String screenshotPath = "test-results/screenshots/" + fileName + ".png";
            currentPage.screenshot(new Page.ScreenshotOptions().setPath(Paths.get(screenshotPath)));
            logger.info("Screenshot saved: {}", screenshotPath);
            return screenshotPath;
        }
        return null;
    }
    
    /**
     * Close current page
     */
    public static void closePage() {
        Page currentPage = getPage();
        if (currentPage != null) {
            currentPage.close();
            page.remove();
        }
    }
    
    /**
     * Close browser context
     */
    public static void closeContext() {
        BrowserContext currentContext = getContext();
        if (currentContext != null) {
            currentContext.close();
            context.remove();
        }
    }
    
    /**
     * Close browser
     */
    public static void closeBrowser() {
        Browser currentBrowser = getBrowser();
        if (currentBrowser != null) {
            currentBrowser.close();
            browser.remove();
        }
    }
    
    /**
     * Quit driver and cleanup resources
     */
    public static void quitDriver() {
        try {
            closePage();
            closeContext();
            closeBrowser();
            
            Playwright currentPlaywright = getPlaywright();
            if (currentPlaywright != null) {
                currentPlaywright.close();
                playwright.remove();
            }
            
            logger.info("Driver cleanup completed successfully");
            
        } catch (Exception e) {
            logger.error("Error during driver cleanup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get browser information
     */
    public static String getBrowserInfo() {
        Browser currentBrowser = getBrowser();
        if (currentBrowser != null) {
            return currentBrowser.browserType().name() + " " + currentBrowser.version();
        }
        return "Browser not initialized";
    }
}
