package com.automation.core;

import com.automation.config.ConfigManager;
import com.automation.reporting.ExtentManager;
import com.automation.utils.ScreenshotUtils;
import com.aventstack.extentreports.ExtentTest;
import com.microsoft.playwright.Page;
import io.qameta.allure.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.ITestResult;
import org.testng.annotations.*;

import java.lang.reflect.Method;

/**
 * Base Test Class for all test classes
 * Provides common setup, teardown, and utility methods
 */
public class TestBase {
    
    protected static final Logger logger = LoggerFactory.getLogger(TestBase.class);
    protected static ThreadLocal<ExtentTest> extentTest = new ThreadLocal<>();
    
    @BeforeSuite(alwaysRun = true)
    public void beforeSuite() {
        logger.info("=== Test Suite Started ===");
        
        // Initialize configuration
        ConfigManager.loadConfiguration();
        
        // Initialize ExtentReports
        ExtentManager.initializeExtentReports();
        
        // Create test results directories
        createTestDirectories();
        
        logger.info("Suite setup completed successfully");
    }
    
    @BeforeClass(alwaysRun = true)
    public void beforeClass() {
        logger.info("=== Test Class Started: {} ===", this.getClass().getSimpleName());
    }
    
    @BeforeMethod(alwaysRun = true)
    public void beforeMethod(Method method) {
        logger.info("=== Test Method Started: {} ===", method.getName());
        
        try {
            // Initialize driver for each test method
            DriverManager.initializeDriver();
            
            // Create ExtentTest instance
            ExtentTest test = ExtentManager.createTest(method.getName(), 
                getTestDescription(method));
            extentTest.set(test);
            
            // Log test start
            logTestInfo("Test started: " + method.getName());
            
        } catch (Exception e) {
            logger.error("Failed to setup test method: {}", e.getMessage(), e);
            throw new RuntimeException("Test setup failed", e);
        }
    }
    
    @AfterMethod(alwaysRun = true)
    public void afterMethod(ITestResult result) {
        String methodName = result.getMethod().getMethodName();
        
        try {
            // Handle test result
            handleTestResult(result);
            
            // Take screenshot for failed tests
            if (result.getStatus() == ITestResult.FAILURE) {
                captureScreenshotOnFailure(methodName);
            }
            
            logger.info("=== Test Method Completed: {} ===", methodName);
            
        } catch (Exception e) {
            logger.error("Error in test cleanup: {}", e.getMessage(), e);
        } finally {
            // Always cleanup driver
            DriverManager.quitDriver();
            
            // Remove ExtentTest from ThreadLocal
            extentTest.remove();
        }
    }
    
    @AfterClass(alwaysRun = true)
    public void afterClass() {
        logger.info("=== Test Class Completed: {} ===", this.getClass().getSimpleName());
    }
    
    @AfterSuite(alwaysRun = true)
    public void afterSuite() {
        logger.info("=== Test Suite Completed ===");
        
        // Flush ExtentReports
        ExtentManager.flushReports();
        
        logger.info("Suite cleanup completed successfully");
    }
    
    /**
     * Get current page instance
     */
    protected Page getPage() {
        return DriverManager.getPage();
    }
    
    /**
     * Navigate to URL
     */
    protected void navigateTo(String url) {
        DriverManager.navigateTo(url);
        logTestInfo("Navigated to: " + url);
    }
    
    /**
     * Log test information
     */
    protected void logTestInfo(String message) {
        logger.info(message);
        ExtentTest test = extentTest.get();
        if (test != null) {
            test.info(message);
        }
    }
    
    /**
     * Log test pass
     */
    protected void logTestPass(String message) {
        logger.info("PASS: {}", message);
        ExtentTest test = extentTest.get();
        if (test != null) {
            test.pass(message);
        }
    }
    
    /**
     * Log test fail
     */
    protected void logTestFail(String message) {
        logger.error("FAIL: {}", message);
        ExtentTest test = extentTest.get();
        if (test != null) {
            test.fail(message);
        }
    }
    
    /**
     * Log test warning
     */
    protected void logTestWarning(String message) {
        logger.warn("WARNING: {}", message);
        ExtentTest test = extentTest.get();
        if (test != null) {
            test.warning(message);
        }
    }
    
    /**
     * Take screenshot and attach to report
     */
    protected void takeScreenshot(String description) {
        try {
            String screenshotPath = ScreenshotUtils.captureScreenshot(description);
            
            // Attach to ExtentReports
            ExtentTest test = extentTest.get();
            if (test != null && screenshotPath != null) {
                test.addScreenCaptureFromPath(screenshotPath, description);
            }
            
            // Attach to Allure
            attachScreenshotToAllure();
            
        } catch (Exception e) {
            logger.error("Failed to take screenshot: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Handle test result based on status
     */
    private void handleTestResult(ITestResult result) {
        ExtentTest test = extentTest.get();
        if (test == null) return;
        
        switch (result.getStatus()) {
            case ITestResult.SUCCESS:
                test.pass("Test passed successfully");
                logger.info("Test PASSED: {}", result.getMethod().getMethodName());
                break;
                
            case ITestResult.FAILURE:
                test.fail("Test failed: " + result.getThrowable().getMessage());
                logger.error("Test FAILED: {}", result.getMethod().getMethodName(), 
                    result.getThrowable());
                break;
                
            case ITestResult.SKIP:
                test.skip("Test skipped: " + result.getThrowable().getMessage());
                logger.warn("Test SKIPPED: {}", result.getMethod().getMethodName());
                break;
        }
    }
    
    /**
     * Capture screenshot on test failure
     */
    private void captureScreenshotOnFailure(String testName) {
        try {
            String screenshotName = "FAILED_" + testName + "_" + System.currentTimeMillis();
            takeScreenshot(screenshotName);
            
        } catch (Exception e) {
            logger.error("Failed to capture failure screenshot: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Attach screenshot to Allure report
     */
    @Attachment(value = "Screenshot", type = "image/png")
    private byte[] attachScreenshotToAllure() {
        return DriverManager.takeScreenshot();
    }
    
    /**
     * Get test description from method annotation or default
     */
    private String getTestDescription(Method method) {
        Test testAnnotation = method.getAnnotation(Test.class);
        if (testAnnotation != null && !testAnnotation.description().isEmpty()) {
            return testAnnotation.description();
        }
        return "Test method: " + method.getName();
    }
    
    /**
     * Create necessary test directories
     */
    private void createTestDirectories() {
        try {
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get("test-results"));
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get("test-results/screenshots"));
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get("test-results/videos"));
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get("test-results/reports"));
            
        } catch (Exception e) {
            logger.error("Failed to create test directories: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get current ExtentTest instance
     */
    protected ExtentTest getExtentTest() {
        return extentTest.get();
    }
}
