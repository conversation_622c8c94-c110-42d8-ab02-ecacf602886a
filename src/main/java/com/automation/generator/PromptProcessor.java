package com.automation.generator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Prompt Processor for analyzing and enhancing user prompts
 * Converts natural language prompts into structured test generation requests
 */
public class PromptProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptProcessor.class);
    
    // Common test action patterns
    private static final List<String> NAVIGATION_KEYWORDS = Arrays.asList(
        "navigate", "go to", "visit", "open", "load", "browse"
    );
    
    private static final List<String> CLICK_KEYWORDS = Arrays.asList(
        "click", "press", "tap", "select", "choose", "hit"
    );
    
    private static final List<String> INPUT_KEYWORDS = Arrays.asList(
        "type", "enter", "input", "fill", "write", "insert"
    );
    
    private static final List<String> VERIFICATION_KEYWORDS = Arrays.asList(
        "verify", "check", "assert", "validate", "confirm", "ensure"
    );
    
    private static final List<String> WAIT_KEYWORDS = Arrays.asList(
        "wait", "pause", "delay", "sleep"
    );
    
    /**
     * Process user prompt and enhance it for AI test generation
     */
    public String processPrompt(String userPrompt, String targetUrl) {
        logger.info("Processing prompt: {}", userPrompt);
        
        try {
            // Analyze prompt structure
            PromptAnalysis analysis = analyzePrompt(userPrompt);
            
            // Build enhanced prompt for AI
            String enhancedPrompt = buildEnhancedPrompt(userPrompt, targetUrl, analysis);
            
            logger.debug("Enhanced prompt: {}", enhancedPrompt);
            return enhancedPrompt;
            
        } catch (Exception e) {
            logger.error("Failed to process prompt: {}", e.getMessage(), e);
            return buildBasicPrompt(userPrompt, targetUrl);
        }
    }
    
    /**
     * Analyze user prompt to identify test patterns
     */
    private PromptAnalysis analyzePrompt(String prompt) {
        PromptAnalysis analysis = new PromptAnalysis();
        String lowerPrompt = prompt.toLowerCase();
        
        // Detect action types
        analysis.hasNavigation = containsAnyKeyword(lowerPrompt, NAVIGATION_KEYWORDS);
        analysis.hasClicks = containsAnyKeyword(lowerPrompt, CLICK_KEYWORDS);
        analysis.hasInputs = containsAnyKeyword(lowerPrompt, INPUT_KEYWORDS);
        analysis.hasVerifications = containsAnyKeyword(lowerPrompt, VERIFICATION_KEYWORDS);
        analysis.hasWaits = containsAnyKeyword(lowerPrompt, WAIT_KEYWORDS);
        
        // Extract specific elements
        analysis.extractedElements = extractUIElements(prompt);
        analysis.extractedData = extractTestData(prompt);
        analysis.extractedUrls = extractUrls(prompt);
        
        // Determine test complexity
        analysis.complexity = determineComplexity(analysis);
        
        // Identify test type
        analysis.testType = identifyTestType(lowerPrompt);
        
        logger.debug("Prompt analysis: {}", analysis);
        return analysis;
    }
    
    /**
     * Check if prompt contains any of the specified keywords
     */
    private boolean containsAnyKeyword(String text, List<String> keywords) {
        return keywords.stream().anyMatch(text::contains);
    }
    
    /**
     * Extract UI elements from prompt
     */
    private List<String> extractUIElements(String prompt) {
        // Patterns for common UI elements
        Pattern elementPattern = Pattern.compile(
            "\\b(button|link|input|field|dropdown|checkbox|radio|menu|form|table|div|span)\\b",
            Pattern.CASE_INSENSITIVE
        );
        
        return extractMatches(prompt, elementPattern);
    }
    
    /**
     * Extract test data from prompt
     */
    private List<String> extractTestData(String prompt) {
        // Patterns for quoted strings and email addresses
        Pattern dataPattern = Pattern.compile(
            "\"([^\"]+)\"|'([^']+)'|\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"
        );
        
        return extractMatches(prompt, dataPattern);
    }
    
    /**
     * Extract URLs from prompt
     */
    private List<String> extractUrls(String prompt) {
        Pattern urlPattern = Pattern.compile(
            "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+",
            Pattern.CASE_INSENSITIVE
        );
        
        return extractMatches(prompt, urlPattern);
    }
    
    /**
     * Extract matches from text using pattern
     */
    private List<String> extractMatches(String text, Pattern pattern) {
        Matcher matcher = pattern.matcher(text);
        return matcher.results()
                .map(result -> result.group())
                .distinct()
                .toList();
    }
    
    /**
     * Determine test complexity based on analysis
     */
    private String determineComplexity(PromptAnalysis analysis) {
        int complexityScore = 0;
        
        if (analysis.hasNavigation) complexityScore++;
        if (analysis.hasClicks) complexityScore++;
        if (analysis.hasInputs) complexityScore++;
        if (analysis.hasVerifications) complexityScore++;
        if (analysis.hasWaits) complexityScore++;
        
        complexityScore += analysis.extractedElements.size();
        complexityScore += analysis.extractedData.size();
        
        if (complexityScore <= 2) return "SIMPLE";
        if (complexityScore <= 5) return "MEDIUM";
        return "COMPLEX";
    }
    
    /**
     * Identify test type from prompt
     */
    private String identifyTestType(String prompt) {
        if (prompt.contains("login") || prompt.contains("sign in") || prompt.contains("authenticate")) {
            return "LOGIN";
        }
        if (prompt.contains("register") || prompt.contains("sign up") || prompt.contains("create account")) {
            return "REGISTRATION";
        }
        if (prompt.contains("search") || prompt.contains("find") || prompt.contains("filter")) {
            return "SEARCH";
        }
        if (prompt.contains("form") || prompt.contains("submit") || prompt.contains("save")) {
            return "FORM";
        }
        if (prompt.contains("api") || prompt.contains("endpoint") || prompt.contains("service")) {
            return "API";
        }
        return "FUNCTIONAL";
    }
    
    /**
     * Build enhanced prompt for AI test generation
     */
    private String buildEnhancedPrompt(String originalPrompt, String targetUrl, PromptAnalysis analysis) {
        StringBuilder enhancedPrompt = new StringBuilder();
        
        enhancedPrompt.append("Generate comprehensive test cases in JSON format for the following scenario:\n\n");
        enhancedPrompt.append("Original Request: ").append(originalPrompt).append("\n");
        enhancedPrompt.append("Target URL: ").append(targetUrl).append("\n");
        enhancedPrompt.append("Test Type: ").append(analysis.testType).append("\n");
        enhancedPrompt.append("Complexity: ").append(analysis.complexity).append("\n\n");
        
        enhancedPrompt.append("Requirements:\n");
        enhancedPrompt.append("1. Generate test cases with proper Playwright selectors\n");
        enhancedPrompt.append("2. Include comprehensive assertions and verifications\n");
        enhancedPrompt.append("3. Add appropriate wait conditions\n");
        enhancedPrompt.append("4. Consider edge cases and error scenarios\n");
        enhancedPrompt.append("5. Use Page Object Model patterns where applicable\n\n");
        
        if (!analysis.extractedElements.isEmpty()) {
            enhancedPrompt.append("Identified UI Elements: ").append(String.join(", ", analysis.extractedElements)).append("\n");
        }
        
        if (!analysis.extractedData.isEmpty()) {
            enhancedPrompt.append("Test Data Found: ").append(String.join(", ", analysis.extractedData)).append("\n");
        }
        
        enhancedPrompt.append("\nPlease return the response in the following JSON format:\n");
        enhancedPrompt.append("{\n");
        enhancedPrompt.append("  \"testCases\": [\n");
        enhancedPrompt.append("    {\n");
        enhancedPrompt.append("      \"name\": \"Test Case Name\",\n");
        enhancedPrompt.append("      \"description\": \"Detailed description\",\n");
        enhancedPrompt.append("      \"priority\": \"HIGH|MEDIUM|LOW\",\n");
        enhancedPrompt.append("      \"steps\": [\n");
        enhancedPrompt.append("        {\n");
        enhancedPrompt.append("          \"action\": \"navigate|click|type|verify|wait\",\n");
        enhancedPrompt.append("          \"target\": \"CSS selector or URL\",\n");
        enhancedPrompt.append("          \"value\": \"input value or expected text\",\n");
        enhancedPrompt.append("          \"description\": \"Step description\"\n");
        enhancedPrompt.append("        }\n");
        enhancedPrompt.append("      ]\n");
        enhancedPrompt.append("    }\n");
        enhancedPrompt.append("  ]\n");
        enhancedPrompt.append("}\n");
        
        return enhancedPrompt.toString();
    }
    
    /**
     * Build basic prompt when analysis fails
     */
    private String buildBasicPrompt(String originalPrompt, String targetUrl) {
        return String.format(
            "Generate test cases for: %s\nTarget URL: %s\n\n" +
            "Please provide test steps in JSON format with actions like navigate, click, type, and verify.",
            originalPrompt, targetUrl
        );
    }
    
    /**
     * Extract test scenarios from complex prompts
     */
    public List<String> extractTestScenarios(String prompt) {
        // Split prompt by common scenario separators
        String[] scenarios = prompt.split("(?i)\\b(and|then|also|additionally|next|after that)\\b");
        
        return Arrays.stream(scenarios)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
    }
    
    /**
     * Validate prompt for test generation
     */
    public boolean isValidPrompt(String prompt) {
        if (prompt == null || prompt.trim().isEmpty()) {
            return false;
        }
        
        // Check minimum length
        if (prompt.trim().length() < 10) {
            return false;
        }
        
        // Check for at least one action keyword
        String lowerPrompt = prompt.toLowerCase();
        return containsAnyKeyword(lowerPrompt, NAVIGATION_KEYWORDS) ||
               containsAnyKeyword(lowerPrompt, CLICK_KEYWORDS) ||
               containsAnyKeyword(lowerPrompt, INPUT_KEYWORDS) ||
               containsAnyKeyword(lowerPrompt, VERIFICATION_KEYWORDS);
    }
    
    /**
     * Inner class for prompt analysis results
     */
    private static class PromptAnalysis {
        boolean hasNavigation = false;
        boolean hasClicks = false;
        boolean hasInputs = false;
        boolean hasVerifications = false;
        boolean hasWaits = false;
        List<String> extractedElements = List.of();
        List<String> extractedData = List.of();
        List<String> extractedUrls = List.of();
        String complexity = "SIMPLE";
        String testType = "FUNCTIONAL";
        
        @Override
        public String toString() {
            return String.format(
                "PromptAnalysis{nav=%s, clicks=%s, inputs=%s, verify=%s, waits=%s, " +
                "elements=%d, data=%d, urls=%d, complexity=%s, type=%s}",
                hasNavigation, hasClicks, hasInputs, hasVerifications, hasWaits,
                extractedElements.size(), extractedData.size(), extractedUrls.size(),
                complexity, testType
            );
        }
    }
}
