package com.automation.generator;

import com.automation.config.ConfigManager;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.chat.ChatLanguageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * AI-Powered Test Generator
 * Generates test cases from natural language prompts using LLM integration
 */
public class TestGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(TestGenerator.class);
    private final ChatLanguageModel chatModel;
    private final ObjectMapper objectMapper;
    private final PromptProcessor promptProcessor;
    
    public TestGenerator() {
        this.objectMapper = new ObjectMapper();
        this.promptProcessor = new PromptProcessor();
        this.chatModel = initializeChatModel();
    }
    
    /**
     * Initialize Chat Language Model
     */
    private ChatLanguageModel initializeChatModel() {
        try {
            String apiKey = ConfigManager.getOpenAiApiKey();
            if (apiKey == null || apiKey.isEmpty()) {
                logger.warn("OpenAI API key not configured. Test generation will use mock responses.");
                return null;
            }
            
            return OpenAiChatModel.builder()
                    .apiKey(apiKey)
                    .modelName("gpt-4")
                    .temperature(0.3)
                    .maxTokens(2000)
                    .build();
                    
        } catch (Exception e) {
            logger.error("Failed to initialize chat model: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Generate test cases from user prompt
     */
    public List<TestCase> generateTestCases(String userPrompt, String targetUrl) {
        logger.info("Generating test cases for prompt: {}", userPrompt);
        
        try {
            // Process the prompt to extract test scenarios
            String processedPrompt = promptProcessor.processPrompt(userPrompt, targetUrl);
            
            // Generate test cases using AI
            String aiResponse = generateAIResponse(processedPrompt);
            
            // Parse AI response to test cases
            List<TestCase> testCases = parseAIResponseToTestCases(aiResponse, userPrompt, targetUrl);
            
            // Save generated test cases
            saveGeneratedTestCases(testCases);
            
            // Generate Java test files
            generateJavaTestFiles(testCases);
            
            logger.info("Successfully generated {} test cases", testCases.size());
            return testCases;
            
        } catch (Exception e) {
            logger.error("Failed to generate test cases: {}", e.getMessage(), e);
            return generateFallbackTestCases(userPrompt, targetUrl);
        }
    }
    
    /**
     * Generate AI response for test case creation
     */
    private String generateAIResponse(String prompt) {
        if (chatModel == null) {
            return generateMockAIResponse();
        }
        
        try {
            String response = chatModel.generate(prompt);
            logger.debug("AI Response: {}", response);
            return response;
            
        } catch (Exception e) {
            logger.error("Failed to get AI response: {}", e.getMessage(), e);
            return generateMockAIResponse();
        }
    }
    
    /**
     * Generate mock AI response for testing
     */
    private String generateMockAIResponse() {
        return """
            {
              "testCases": [
                {
                  "name": "Login Functionality Test",
                  "description": "Test user login with valid credentials",
                  "priority": "HIGH",
                  "steps": [
                    {
                      "action": "navigate",
                      "target": "login page",
                      "value": "",
                      "description": "Navigate to login page"
                    },
                    {
                      "action": "type",
                      "target": "input[name='username']",
                      "value": "<EMAIL>",
                      "description": "Enter username"
                    },
                    {
                      "action": "type",
                      "target": "input[name='password']",
                      "value": "password123",
                      "description": "Enter password"
                    },
                    {
                      "action": "click",
                      "target": "button[type='submit']",
                      "value": "",
                      "description": "Click login button"
                    },
                    {
                      "action": "verify",
                      "target": ".dashboard",
                      "value": "visible",
                      "description": "Verify dashboard is visible"
                    }
                  ]
                }
              ]
            }
            """;
    }
    
    /**
     * Parse AI response to TestCase objects
     */
    private List<TestCase> parseAIResponseToTestCases(String aiResponse, String originalPrompt, String targetUrl) {
        List<TestCase> testCases = new ArrayList<>();
        
        try {
            // Try to parse JSON response
            if (aiResponse.contains("{") && aiResponse.contains("}")) {
                // Extract JSON from response
                String jsonPart = extractJsonFromResponse(aiResponse);
                
                // Parse JSON to test cases
                TestCaseResponse response = objectMapper.readValue(jsonPart, TestCaseResponse.class);
                testCases = response.getTestCases();
            } else {
                // Parse plain text response
                testCases = parseTextResponseToTestCases(aiResponse, originalPrompt, targetUrl);
            }
            
            // Set additional properties
            for (TestCase testCase : testCases) {
                testCase.setId(UUID.randomUUID().toString());
                testCase.setCreatedAt(LocalDateTime.now());
                testCase.setTargetUrl(targetUrl);
                testCase.setOriginalPrompt(originalPrompt);
            }
            
        } catch (Exception e) {
            logger.error("Failed to parse AI response: {}", e.getMessage(), e);
            testCases = generateFallbackTestCases(originalPrompt, targetUrl);
        }
        
        return testCases;
    }
    
    /**
     * Extract JSON from AI response
     */
    private String extractJsonFromResponse(String response) {
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}") + 1;
        
        if (startIndex >= 0 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex);
        }
        
        return response;
    }
    
    /**
     * Parse text response to test cases
     */
    private List<TestCase> parseTextResponseToTestCases(String textResponse, String originalPrompt, String targetUrl) {
        List<TestCase> testCases = new ArrayList<>();
        
        // Simple text parsing logic
        TestCase testCase = new TestCase();
        testCase.setName("Generated Test from Prompt");
        testCase.setDescription("Test case generated from: " + originalPrompt);
        testCase.setPriority("MEDIUM");
        
        List<TestStep> steps = new ArrayList<>();
        
        // Add basic navigation step
        TestStep navStep = new TestStep();
        navStep.setAction("navigate");
        navStep.setTarget(targetUrl);
        navStep.setDescription("Navigate to target URL");
        steps.add(navStep);
        
        // Add steps based on text analysis
        if (textResponse.toLowerCase().contains("click")) {
            TestStep clickStep = new TestStep();
            clickStep.setAction("click");
            clickStep.setTarget("button");
            clickStep.setDescription("Click button");
            steps.add(clickStep);
        }
        
        if (textResponse.toLowerCase().contains("type") || textResponse.toLowerCase().contains("enter")) {
            TestStep typeStep = new TestStep();
            typeStep.setAction("type");
            typeStep.setTarget("input");
            typeStep.setValue("test data");
            typeStep.setDescription("Enter test data");
            steps.add(typeStep);
        }
        
        testCase.setSteps(steps);
        testCases.add(testCase);
        
        return testCases;
    }
    
    /**
     * Generate fallback test cases when AI fails
     */
    private List<TestCase> generateFallbackTestCases(String originalPrompt, String targetUrl) {
        List<TestCase> testCases = new ArrayList<>();
        
        TestCase fallbackTest = new TestCase();
        fallbackTest.setId(UUID.randomUUID().toString());
        fallbackTest.setName("Fallback Test Case");
        fallbackTest.setDescription("Fallback test generated for: " + originalPrompt);
        fallbackTest.setPriority("LOW");
        fallbackTest.setCreatedAt(LocalDateTime.now());
        fallbackTest.setTargetUrl(targetUrl);
        fallbackTest.setOriginalPrompt(originalPrompt);
        
        List<TestStep> steps = new ArrayList<>();
        
        TestStep navStep = new TestStep();
        navStep.setAction("navigate");
        navStep.setTarget(targetUrl);
        navStep.setDescription("Navigate to target URL");
        steps.add(navStep);
        
        TestStep verifyStep = new TestStep();
        verifyStep.setAction("verify");
        verifyStep.setTarget("body");
        verifyStep.setValue("visible");
        verifyStep.setDescription("Verify page is loaded");
        steps.add(verifyStep);
        
        fallbackTest.setSteps(steps);
        testCases.add(fallbackTest);
        
        return testCases;
    }
    
    /**
     * Save generated test cases to JSON file
     */
    private void saveGeneratedTestCases(List<TestCase> testCases) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = "generated_tests_" + timestamp + ".json";
            String filePath = ConfigManager.getTestDataPath() + "/generated/" + fileName;
            
            // Create directory if not exists
            File directory = new File(ConfigManager.getTestDataPath() + "/generated");
            directory.mkdirs();
            
            // Write test cases to file
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(new File(filePath), testCases);
            
            logger.info("Generated test cases saved to: {}", filePath);
            
        } catch (Exception e) {
            logger.error("Failed to save generated test cases: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Generate Java test files from test cases
     */
    private void generateJavaTestFiles(List<TestCase> testCases) {
        for (TestCase testCase : testCases) {
            try {
                String javaCode = generateJavaTestCode(testCase);
                saveJavaTestFile(testCase, javaCode);
                
            } catch (Exception e) {
                logger.error("Failed to generate Java test file for: {}", testCase.getName(), e);
            }
        }
    }
    
    /**
     * Generate Java test code from test case
     */
    private String generateJavaTestCode(TestCase testCase) {
        StringBuilder code = new StringBuilder();
        
        code.append("package com.automation.tests.generated;\n\n");
        code.append("import com.automation.core.TestBase;\n");
        code.append("import com.microsoft.playwright.Page;\n");
        code.append("import org.testng.annotations.Test;\n");
        code.append("import org.testng.Assert;\n\n");
        
        String className = testCase.getName().replaceAll("[^a-zA-Z0-9]", "") + "Test";
        code.append("/**\n");
        code.append(" * Generated Test Class: ").append(testCase.getName()).append("\n");
        code.append(" * Description: ").append(testCase.getDescription()).append("\n");
        code.append(" * Generated from prompt: ").append(testCase.getOriginalPrompt()).append("\n");
        code.append(" */\n");
        code.append("public class ").append(className).append(" extends TestBase {\n\n");
        
        code.append("    @Test(description = \"").append(testCase.getDescription()).append("\")\n");
        code.append("    public void ").append(testCase.getName().replaceAll("[^a-zA-Z0-9]", "").toLowerCase()).append("() {\n");
        code.append("        Page page = getPage();\n\n");
        
        for (TestStep step : testCase.getSteps()) {
            code.append("        // ").append(step.getDescription()).append("\n");
            code.append(generateStepCode(step));
            code.append("\n");
        }
        
        code.append("    }\n");
        code.append("}\n");
        
        return code.toString();
    }
    
    /**
     * Generate code for individual test step
     */
    private String generateStepCode(TestStep step) {
        switch (step.getAction().toLowerCase()) {
            case "navigate":
                return "        page.navigate(\"" + step.getTarget() + "\");";
            case "click":
                return "        page.click(\"" + step.getTarget() + "\");";
            case "type":
                return "        page.fill(\"" + step.getTarget() + "\", \"" + step.getValue() + "\");";
            case "verify":
                if ("visible".equals(step.getValue())) {
                    return "        Assert.assertTrue(page.isVisible(\"" + step.getTarget() + "\"));";
                } else {
                    return "        Assert.assertEquals(page.textContent(\"" + step.getTarget() + "\"), \"" + step.getValue() + "\");";
                }
            default:
                return "        // TODO: Implement action: " + step.getAction();
        }
    }
    
    /**
     * Save Java test file
     */
    private void saveJavaTestFile(TestCase testCase, String javaCode) throws IOException {
        String className = testCase.getName().replaceAll("[^a-zA-Z0-9]", "") + "Test";
        String fileName = className + ".java";
        String filePath = "src/test/java/com/automation/tests/generated/" + fileName;
        
        // Create directory if not exists
        File directory = new File("src/test/java/com/automation/tests/generated");
        directory.mkdirs();
        
        // Write Java code to file
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(javaCode);
        }
        
        logger.info("Generated Java test file: {}", filePath);
    }
    
    /**
     * Inner class for parsing AI response
     */
    private static class TestCaseResponse {
        private List<TestCase> testCases;
        
        public List<TestCase> getTestCases() {
            return testCases;
        }
        
        public void setTestCases(List<TestCase> testCases) {
            this.testCases = testCases;
        }
    }
}
