package com.automation.utils;

import com.automation.config.ConfigManager;
import com.automation.core.DriverManager;
import com.microsoft.playwright.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * Screenshot Utilities
 * Provides methods for capturing and managing screenshots
 */
public class ScreenshotUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ScreenshotUtils.class);
    private static final String SCREENSHOT_FORMAT = "png";
    
    /**
     * Capture screenshot with custom name
     */
    public static String captureScreenshot(String testName) {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Page is null, cannot capture screenshot");
                return null;
            }
            
            // Create screenshots directory
            String screenshotsDir = ConfigManager.getScreenshotsPath();
            createDirectoryIfNotExists(screenshotsDir);
            
            // Generate screenshot filename
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String sanitizedTestName = sanitizeFileName(testName);
            String fileName = sanitizedTestName + "_" + timestamp + "." + SCREENSHOT_FORMAT;
            String filePath = screenshotsDir + "/" + fileName;
            
            // Capture screenshot
            page.screenshot(new Page.ScreenshotOptions()
                    .setPath(Paths.get(filePath))
                    .setFullPage(true));
            
            logger.info("Screenshot captured: {}", filePath);
            return filePath;
            
        } catch (Exception e) {
            logger.error("Failed to capture screenshot: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Capture screenshot with default name
     */
    public static String captureScreenshot() {
        String defaultName = "screenshot_" + System.currentTimeMillis();
        return captureScreenshot(defaultName);
    }
    
    /**
     * Capture screenshot of specific element
     */
    public static String captureElementScreenshot(String selector, String testName) {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Page is null, cannot capture element screenshot");
                return null;
            }
            
            // Create screenshots directory
            String screenshotsDir = ConfigManager.getScreenshotsPath();
            createDirectoryIfNotExists(screenshotsDir);
            
            // Generate screenshot filename
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String sanitizedTestName = sanitizeFileName(testName);
            String fileName = sanitizedTestName + "_element_" + timestamp + "." + SCREENSHOT_FORMAT;
            String filePath = screenshotsDir + "/" + fileName;
            
            // Capture element screenshot
            page.locator(selector).screenshot(new com.microsoft.playwright.Locator.ScreenshotOptions()
                    .setPath(Paths.get(filePath)));
            
            logger.info("Element screenshot captured: {}", filePath);
            return filePath;
            
        } catch (Exception e) {
            logger.error("Failed to capture element screenshot: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Capture screenshot as base64 string
     */
    public static String captureScreenshotAsBase64() {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Page is null, cannot capture screenshot");
                return null;
            }
            
            byte[] screenshotBytes = page.screenshot(new Page.ScreenshotOptions().setFullPage(true));
            String base64Screenshot = Base64.getEncoder().encodeToString(screenshotBytes);
            
            logger.debug("Screenshot captured as base64 (length: {})", base64Screenshot.length());
            return base64Screenshot;
            
        } catch (Exception e) {
            logger.error("Failed to capture screenshot as base64: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Capture screenshot with custom options
     */
    public static String captureScreenshotWithOptions(String testName, boolean fullPage, int quality) {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Page is null, cannot capture screenshot");
                return null;
            }
            
            // Create screenshots directory
            String screenshotsDir = ConfigManager.getScreenshotsPath();
            createDirectoryIfNotExists(screenshotsDir);
            
            // Generate screenshot filename
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String sanitizedTestName = sanitizeFileName(testName);
            String fileName = sanitizedTestName + "_" + timestamp + "." + SCREENSHOT_FORMAT;
            String filePath = screenshotsDir + "/" + fileName;
            
            // Configure screenshot options
            Page.ScreenshotOptions options = new Page.ScreenshotOptions()
                    .setPath(Paths.get(filePath))
                    .setFullPage(fullPage);
            
            // Set quality for JPEG format
            if (quality > 0 && quality <= 100) {
                options.setQuality(quality);
            }
            
            // Capture screenshot
            page.screenshot(options);
            
            logger.info("Screenshot captured with options: {}", filePath);
            return filePath;
            
        } catch (Exception e) {
            logger.error("Failed to capture screenshot with options: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Capture screenshot on test failure
     */
    public static String captureFailureScreenshot(String testName, String errorMessage) {
        try {
            String screenshotPath = captureScreenshot("FAILED_" + testName);
            
            if (screenshotPath != null) {
                // Create failure info file
                createFailureInfoFile(screenshotPath, testName, errorMessage);
            }
            
            return screenshotPath;
            
        } catch (Exception e) {
            logger.error("Failed to capture failure screenshot: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Create failure information file
     */
    private static void createFailureInfoFile(String screenshotPath, String testName, String errorMessage) {
        try {
            String infoFilePath = screenshotPath.replace("." + SCREENSHOT_FORMAT, "_info.txt");
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            String failureInfo = String.format(
                "Test Failure Information\n" +
                "========================\n" +
                "Test Name: %s\n" +
                "Timestamp: %s\n" +
                "Screenshot: %s\n" +
                "Error Message: %s\n" +
                "Browser: %s\n" +
                "Environment: %s\n",
                testName, timestamp, screenshotPath, errorMessage,
                ConfigManager.getBrowser(), ConfigManager.getProperty("environment", "dev")
            );
            
            Files.write(Paths.get(infoFilePath), failureInfo.getBytes());
            logger.debug("Failure info file created: {}", infoFilePath);
            
        } catch (Exception e) {
            logger.error("Failed to create failure info file: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Compare screenshots (basic implementation)
     */
    public static boolean compareScreenshots(String screenshot1Path, String screenshot2Path) {
        try {
            byte[] image1 = Files.readAllBytes(Paths.get(screenshot1Path));
            byte[] image2 = Files.readAllBytes(Paths.get(screenshot2Path));
            
            // Basic byte comparison (for exact match)
            boolean isEqual = java.util.Arrays.equals(image1, image2);
            
            logger.info("Screenshot comparison result: {} ({})", isEqual, 
                       isEqual ? "Images are identical" : "Images are different");
            
            return isEqual;
            
        } catch (Exception e) {
            logger.error("Failed to compare screenshots: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Clean up old screenshots
     */
    public static void cleanupOldScreenshots(int daysToKeep) {
        try {
            String screenshotsDir = ConfigManager.getScreenshotsPath();
            File directory = new File(screenshotsDir);
            
            if (!directory.exists()) {
                return;
            }
            
            long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60L * 60L * 1000L);
            File[] files = directory.listFiles();
            
            if (files != null) {
                int deletedCount = 0;
                for (File file : files) {
                    if (file.isFile() && file.lastModified() < cutoffTime) {
                        if (file.delete()) {
                            deletedCount++;
                        }
                    }
                }
                logger.info("Cleaned up {} old screenshots (older than {} days)", deletedCount, daysToKeep);
            }
            
        } catch (Exception e) {
            logger.error("Failed to cleanup old screenshots: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get screenshot file size
     */
    public static long getScreenshotFileSize(String screenshotPath) {
        try {
            Path path = Paths.get(screenshotPath);
            return Files.size(path);
        } catch (Exception e) {
            logger.error("Failed to get screenshot file size: {}", e.getMessage(), e);
            return -1;
        }
    }
    
    /**
     * Check if screenshot file exists
     */
    public static boolean screenshotExists(String screenshotPath) {
        return screenshotPath != null && Files.exists(Paths.get(screenshotPath));
    }
    
    /**
     * Create directory if it doesn't exist
     */
    private static void createDirectoryIfNotExists(String directoryPath) throws IOException {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            logger.debug("Created directory: {}", directoryPath);
        }
    }
    
    /**
     * Sanitize filename by removing invalid characters
     */
    private static String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "screenshot";
        }
        
        // Replace invalid characters with underscores
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_")
                      .replaceAll("_{2,}", "_")  // Replace multiple underscores with single
                      .replaceAll("^_|_$", ""); // Remove leading/trailing underscores
    }
    
    /**
     * Get relative path for reports
     */
    public static String getRelativeScreenshotPath(String absolutePath) {
        try {
            String reportsDir = ConfigManager.getReportsPath();
            String screenshotsDir = ConfigManager.getScreenshotsPath();
            
            // Calculate relative path from reports directory to screenshots directory
            Path reportsPath = Paths.get(reportsDir).toAbsolutePath();
            Path screenshotPath = Paths.get(absolutePath).toAbsolutePath();
            
            return reportsPath.relativize(screenshotPath).toString();
            
        } catch (Exception e) {
            logger.error("Failed to get relative screenshot path: {}", e.getMessage(), e);
            return absolutePath;
        }
    }
}
