package com.automation.reporting;

import com.automation.config.ConfigManager;
import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.reporter.ExtentSparkReporter;
import com.aventstack.extentreports.reporter.configuration.Theme;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ExtentReports Manager
 * Manages ExtentReports configuration and test creation
 */
public class ExtentManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtentManager.class);
    private static ExtentReports extentReports;
    private static String reportPath;
    
    /**
     * Initialize ExtentReports
     */
    public static void initializeExtentReports() {
        if (extentReports == null) {
            try {
                // Create reports directory
                String reportsDir = ConfigManager.getReportsPath();
                File directory = new File(reportsDir);
                directory.mkdirs();
                
                // Generate report file name with timestamp
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                reportPath = reportsDir + "/ExtentReport_" + timestamp + ".html";
                
                // Create ExtentSparkReporter
                ExtentSparkReporter sparkReporter = new ExtentSparkReporter(reportPath);
                configureSparkReporter(sparkReporter);
                
                // Create ExtentReports instance
                extentReports = new ExtentReports();
                extentReports.attachReporter(sparkReporter);
                
                // Set system information
                setSystemInformation();
                
                logger.info("ExtentReports initialized successfully. Report path: {}", reportPath);
                
            } catch (Exception e) {
                logger.error("Failed to initialize ExtentReports: {}", e.getMessage(), e);
                throw new RuntimeException("ExtentReports initialization failed", e);
            }
        }
    }
    
    /**
     * Configure Spark Reporter
     */
    private static void configureSparkReporter(ExtentSparkReporter sparkReporter) {
        sparkReporter.config().setTheme(Theme.DARK);
        sparkReporter.config().setDocumentTitle("Playwright Automation Test Report");
        sparkReporter.config().setReportName("Test Execution Report");
        sparkReporter.config().setTimeStampFormat("yyyy-MM-dd HH:mm:ss");
        
        // Custom CSS for better appearance
        String customCSS = """
            .test-content {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            .step-details {
                background-color: #2d2d2d;
                border-left: 3px solid #4CAF50;
                padding: 10px;
                margin: 5px 0;
            }
            .screenshot-container {
                text-align: center;
                margin: 10px 0;
            }
            .screenshot-container img {
                max-width: 100%;
                border: 2px solid #4CAF50;
                border-radius: 5px;
            }
            """;
        
        sparkReporter.config().setCss(customCSS);
        
        // JavaScript for enhanced functionality
        String customJS = """
            function toggleScreenshot(element) {
                var img = element.nextElementSibling;
                if (img.style.display === 'none') {
                    img.style.display = 'block';
                    element.textContent = 'Hide Screenshot';
                } else {
                    img.style.display = 'none';
                    element.textContent = 'Show Screenshot';
                }
            }
            """;
        
        sparkReporter.config().setJs(customJS);
    }
    
    /**
     * Set system information in the report
     */
    private static void setSystemInformation() {
        extentReports.setSystemInfo("Operating System", System.getProperty("os.name"));
        extentReports.setSystemInfo("OS Version", System.getProperty("os.version"));
        extentReports.setSystemInfo("Java Version", System.getProperty("java.version"));
        extentReports.setSystemInfo("User Name", System.getProperty("user.name"));
        extentReports.setSystemInfo("Environment", ConfigManager.getProperty("environment", "dev"));
        extentReports.setSystemInfo("Browser", ConfigManager.getBrowser());
        extentReports.setSystemInfo("Base URL", ConfigManager.getBaseUrl());
        extentReports.setSystemInfo("Headless Mode", String.valueOf(ConfigManager.isHeadless()));
        extentReports.setSystemInfo("Framework", "Playwright + TestNG + Maven");
        extentReports.setSystemInfo("Report Generated", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }
    
    /**
     * Create a new test in ExtentReports
     */
    public static ExtentTest createTest(String testName, String description) {
        if (extentReports == null) {
            initializeExtentReports();
        }
        
        ExtentTest test = extentReports.createTest(testName, description);
        
        // Add test categories/tags
        test.assignCategory("Automated Test");
        test.assignCategory(ConfigManager.getProperty("testType", "Functional"));
        
        logger.debug("Created ExtentTest: {}", testName);
        return test;
    }
    
    /**
     * Create a child test (for data-driven tests)
     */
    public static ExtentTest createChildTest(ExtentTest parentTest, String childTestName, String description) {
        ExtentTest childTest = parentTest.createNode(childTestName, description);
        logger.debug("Created child ExtentTest: {}", childTestName);
        return childTest;
    }
    
    /**
     * Flush the reports (write to file)
     */
    public static void flushReports() {
        if (extentReports != null) {
            extentReports.flush();
            logger.info("ExtentReports flushed successfully. Report available at: {}", reportPath);
        }
    }
    
    /**
     * Get the current ExtentReports instance
     */
    public static ExtentReports getExtentReports() {
        if (extentReports == null) {
            initializeExtentReports();
        }
        return extentReports;
    }
    
    /**
     * Get the report file path
     */
    public static String getReportPath() {
        return reportPath;
    }
    
    /**
     * Add screenshot to test
     */
    public static void addScreenshot(ExtentTest test, String screenshotPath, String description) {
        try {
            if (screenshotPath != null && !screenshotPath.isEmpty()) {
                test.addScreenCaptureFromPath(screenshotPath, description);
                logger.debug("Screenshot added to test: {}", screenshotPath);
            }
        } catch (Exception e) {
            logger.error("Failed to add screenshot to ExtentTest: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Add base64 screenshot to test
     */
    public static void addBase64Screenshot(ExtentTest test, String base64Screenshot, String description) {
        try {
            if (base64Screenshot != null && !base64Screenshot.isEmpty()) {
                test.addScreenCaptureFromBase64String(base64Screenshot, description);
                logger.debug("Base64 screenshot added to test");
            }
        } catch (Exception e) {
            logger.error("Failed to add base64 screenshot to ExtentTest: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Log test step information
     */
    public static void logStep(ExtentTest test, String stepDescription, String status, String details) {
        String logMessage = String.format("<div class='step-details'><b>Step:</b> %s<br><b>Status:</b> %s<br><b>Details:</b> %s</div>", 
                                         stepDescription, status, details);
        
        switch (status.toUpperCase()) {
            case "PASS":
            case "PASSED":
                test.pass(logMessage);
                break;
            case "FAIL":
            case "FAILED":
                test.fail(logMessage);
                break;
            case "SKIP":
            case "SKIPPED":
                test.skip(logMessage);
                break;
            case "WARNING":
                test.warning(logMessage);
                break;
            default:
                test.info(logMessage);
                break;
        }
    }
    
    /**
     * Add test execution summary
     */
    public static void addExecutionSummary(ExtentTest test, long executionTime, int totalSteps, int passedSteps, int failedSteps) {
        String summary = String.format(
            "<div class='execution-summary'>" +
            "<h4>Execution Summary</h4>" +
            "<p><b>Total Execution Time:</b> %d ms</p>" +
            "<p><b>Total Steps:</b> %d</p>" +
            "<p><b>Passed Steps:</b> %d</p>" +
            "<p><b>Failed Steps:</b> %d</p>" +
            "<p><b>Success Rate:</b> %.2f%%</p>" +
            "</div>",
            executionTime, totalSteps, passedSteps, failedSteps,
            totalSteps > 0 ? (passedSteps * 100.0 / totalSteps) : 0.0
        );
        
        test.info(summary);
    }
    
    /**
     * Add browser information to test
     */
    public static void addBrowserInfo(ExtentTest test, String browserInfo) {
        test.info("<b>Browser:</b> " + browserInfo);
    }
    
    /**
     * Add environment information to test
     */
    public static void addEnvironmentInfo(ExtentTest test, String environment, String baseUrl) {
        test.info("<b>Environment:</b> " + environment);
        test.info("<b>Base URL:</b> " + baseUrl);
    }
    
    /**
     * Cleanup resources
     */
    public static void cleanup() {
        if (extentReports != null) {
            flushReports();
            extentReports = null;
        }
    }
}
