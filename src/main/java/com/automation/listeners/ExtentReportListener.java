package com.automation.listeners;

import com.automation.reporting.ExtentManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.ITestListener;
import org.testng.ITestResult;

/**
 * ExtentReport Listener
 * Additional listener for ExtentReports integration
 */
public class ExtentReportListener implements ITestListener {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtentReportListener.class);
    
    @Override
    public void onTestStart(ITestResult result) {
        logger.debug("ExtentReport: Test started - {}", result.getMethod().getMethodName());
    }
    
    @Override
    public void onTestSuccess(ITestResult result) {
        logger.debug("ExtentReport: Test passed - {}", result.getMethod().getMethodName());
    }
    
    @Override
    public void onTestFailure(ITestResult result) {
        logger.debug("ExtentReport: Test failed - {}", result.getMethod().getMethodName());
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        logger.debug("ExtentReport: Test skipped - {}", result.getMethod().getMethodName());
    }
}
