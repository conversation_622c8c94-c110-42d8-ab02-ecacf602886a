package com.automation.listeners;

import com.automation.config.ConfigManager;
import com.automation.core.DriverManager;
import com.automation.reporting.ExtentManager;
import com.automation.utils.ScreenshotUtils;
import com.aventstack.extentreports.ExtentTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TestNG Listener for test execution events
 * Handles test lifecycle events and integrates with reporting
 */
public class TestListener implements ITestListener, ISuiteListener, IInvokedMethodListener {
    
    private static final Logger logger = LoggerFactory.getLogger(TestListener.class);
    private static final ConcurrentHashMap<String, Long> testStartTimes = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, ExtentTest> extentTests = new ConcurrentHashMap<>();
    
    @Override
    public void onStart(ISuite suite) {
        logger.info("=== Test Suite Started: {} ===", suite.getName());
        
        // Initialize configuration
        ConfigManager.loadConfiguration();
        
        // Initialize ExtentReports
        ExtentManager.initializeExtentReports();
        
        // Log suite information
        logger.info("Suite XML File: {}", suite.getXmlSuite().getFileName());
        logger.info("Parallel Mode: {}", suite.getXmlSuite().getParallel());
        logger.info("Thread Count: {}", suite.getXmlSuite().getThreadCount());
        
        // Print configuration
        if (logger.isDebugEnabled()) {
            ConfigManager.printAllProperties();
        }
    }
    
    @Override
    public void onFinish(ISuite suite) {
        logger.info("=== Test Suite Finished: {} ===", suite.getName());
        
        // Generate suite summary
        generateSuiteSummary(suite);
        
        // Cleanup old screenshots
        int daysToKeep = ConfigManager.getIntProperty("screenshot.retention.days", 7);
        ScreenshotUtils.cleanupOldScreenshots(daysToKeep);
        
        // Flush reports
        ExtentManager.flushReports();
        
        logger.info("Suite execution completed. Report available at: {}", ExtentManager.getReportPath());
    }
    
    @Override
    public void onTestStart(ITestResult result) {
        String testName = getTestName(result);
        logger.info("=== Test Started: {} ===", testName);
        
        // Record test start time
        testStartTimes.put(testName, System.currentTimeMillis());
        
        // Create ExtentTest
        String description = getTestDescription(result);
        ExtentTest extentTest = ExtentManager.createTest(testName, description);
        extentTests.put(testName, extentTest);
        
        // Add test information
        extentTest.assignCategory(getTestCategory(result));
        extentTest.assignAuthor(System.getProperty("user.name", "Automation"));
        
        // Add environment information
        ExtentManager.addEnvironmentInfo(extentTest, 
            ConfigManager.getProperty("environment", "dev"), 
            ConfigManager.getBaseUrl());
        
        // Add browser information
        ExtentManager.addBrowserInfo(extentTest, DriverManager.getBrowserInfo());
        
        // Log test start
        extentTest.info("Test execution started at: " + 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }
    
    @Override
    public void onTestSuccess(ITestResult result) {
        String testName = getTestName(result);
        long executionTime = getExecutionTime(testName);
        
        logger.info("=== Test Passed: {} ({}ms) ===", testName, executionTime);
        
        // Update ExtentTest
        ExtentTest extentTest = extentTests.get(testName);
        if (extentTest != null) {
            extentTest.pass("Test passed successfully");
            extentTest.info("Execution time: " + executionTime + " ms");
            
            // Take success screenshot if configured
            if (ConfigManager.getBooleanProperty("screenshot.on.success", false)) {
                String screenshotPath = ScreenshotUtils.captureScreenshot("SUCCESS_" + testName);
                if (screenshotPath != null) {
                    ExtentManager.addScreenshot(extentTest, screenshotPath, "Test Success Screenshot");
                }
            }
        }
        
        // Cleanup
        cleanupTestData(testName);
    }
    
    @Override
    public void onTestFailure(ITestResult result) {
        String testName = getTestName(result);
        long executionTime = getExecutionTime(testName);
        String errorMessage = getErrorMessage(result);
        
        logger.error("=== Test Failed: {} ({}ms) ===", testName, executionTime);
        logger.error("Error: {}", errorMessage);
        
        // Update ExtentTest
        ExtentTest extentTest = extentTests.get(testName);
        if (extentTest != null) {
            extentTest.fail("Test failed: " + errorMessage);
            extentTest.info("Execution time: " + executionTime + " ms");
            
            // Capture failure screenshot
            String screenshotPath = ScreenshotUtils.captureFailureScreenshot(testName, errorMessage);
            if (screenshotPath != null) {
                ExtentManager.addScreenshot(extentTest, screenshotPath, "Failure Screenshot");
            }
            
            // Add stack trace
            if (result.getThrowable() != null) {
                extentTest.fail(result.getThrowable());
            }
        }
        
        // Cleanup
        cleanupTestData(testName);
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        String testName = getTestName(result);
        String skipReason = getSkipReason(result);
        
        logger.warn("=== Test Skipped: {} ===", testName);
        logger.warn("Skip reason: {}", skipReason);
        
        // Update ExtentTest
        ExtentTest extentTest = extentTests.get(testName);
        if (extentTest != null) {
            extentTest.skip("Test skipped: " + skipReason);
            
            if (result.getThrowable() != null) {
                extentTest.skip(result.getThrowable());
            }
        }
        
        // Cleanup
        cleanupTestData(testName);
    }
    
    @Override
    public void beforeInvocation(IInvokedMethod method, ITestResult testResult) {
        if (method.isTestMethod()) {
            String methodName = method.getTestMethod().getMethodName();
            logger.debug("Before test method invocation: {}", methodName);
        }
    }
    
    @Override
    public void afterInvocation(IInvokedMethod method, ITestResult testResult) {
        if (method.isTestMethod()) {
            String methodName = method.getTestMethod().getMethodName();
            logger.debug("After test method invocation: {}", methodName);
        }
    }
    
    /**
     * Get test name from result
     */
    private String getTestName(ITestResult result) {
        return result.getMethod().getMethodName();
    }
    
    /**
     * Get test description
     */
    private String getTestDescription(ITestResult result) {
        String description = result.getMethod().getDescription();
        if (description == null || description.isEmpty()) {
            description = "Test method: " + getTestName(result);
        }
        return description;
    }
    
    /**
     * Get test category
     */
    private String getTestCategory(ITestResult result) {
        // Try to get category from test groups
        String[] groups = result.getMethod().getGroups();
        if (groups != null && groups.length > 0) {
            return groups[0];
        }
        
        // Default category
        return "Functional";
    }
    
    /**
     * Get execution time for test
     */
    private long getExecutionTime(String testName) {
        Long startTime = testStartTimes.get(testName);
        if (startTime != null) {
            return System.currentTimeMillis() - startTime;
        }
        return 0;
    }
    
    /**
     * Get error message from test result
     */
    private String getErrorMessage(ITestResult result) {
        Throwable throwable = result.getThrowable();
        if (throwable != null) {
            String message = throwable.getMessage();
            return message != null ? message : throwable.getClass().getSimpleName();
        }
        return "Unknown error";
    }
    
    /**
     * Get skip reason from test result
     */
    private String getSkipReason(ITestResult result) {
        Throwable throwable = result.getThrowable();
        if (throwable != null) {
            String message = throwable.getMessage();
            return message != null ? message : "Test skipped due to: " + throwable.getClass().getSimpleName();
        }
        return "Test skipped";
    }
    
    /**
     * Generate suite execution summary
     */
    private void generateSuiteSummary(ISuite suite) {
        try {
            int totalTests = 0;
            int passedTests = 0;
            int failedTests = 0;
            int skippedTests = 0;
            
            for (ISuiteResult suiteResult : suite.getResults().values()) {
                ITestContext testContext = suiteResult.getTestContext();
                totalTests += testContext.getAllTestMethods().length;
                passedTests += testContext.getPassedTests().size();
                failedTests += testContext.getFailedTests().size();
                skippedTests += testContext.getSkippedTests().size();
            }
            
            double successRate = totalTests > 0 ? (passedTests * 100.0 / totalTests) : 0.0;
            
            logger.info("=== Suite Execution Summary ===");
            logger.info("Total Tests: {}", totalTests);
            logger.info("Passed: {}", passedTests);
            logger.info("Failed: {}", failedTests);
            logger.info("Skipped: {}", skippedTests);
            logger.info("Success Rate: {:.2f}%", successRate);
            logger.info("===============================");
            
        } catch (Exception e) {
            logger.error("Failed to generate suite summary: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Cleanup test data
     */
    private void cleanupTestData(String testName) {
        testStartTimes.remove(testName);
        extentTests.remove(testName);
    }
}
