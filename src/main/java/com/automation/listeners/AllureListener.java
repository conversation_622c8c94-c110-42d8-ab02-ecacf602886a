package com.automation.listeners;

import com.automation.utils.ScreenshotUtils;
import io.qameta.allure.Attachment;
import io.qameta.allure.listener.TestLifecycleListener;
import io.qameta.allure.model.Status;
import io.qameta.allure.model.TestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Allure Test Listener
 * Integrates with Allure reporting framework
 */
public class AllureListener implements TestLifecycleListener {
    
    private static final Logger logger = LoggerFactory.getLogger(AllureListener.class);
    
    @Override
    public void beforeTestStart(TestResult result) {
        logger.debug("Allure: Test starting - {}", result.getName());
    }
    
    @Override
    public void beforeTestStop(TestResult result) {
        logger.debug("Allure: Test stopping - {}", result.getName());
        
        // Attach screenshot for failed tests
        if (result.getStatus() == Status.FAILED || result.getStatus() == Status.BROKEN) {
            attachScreenshot();
            attachPageSource();
        }
    }
    
    @Override
    public void afterTestStop(TestResult result) {
        logger.debug("Allure: Test stopped - {} with status {}", result.getName(), result.getStatus());
    }
    
    @Attachment(value = "Screenshot", type = "image/png")
    public byte[] attachScreenshot() {
        try {
            String base64Screenshot = ScreenshotUtils.captureScreenshotAsBase64();
            if (base64Screenshot != null) {
                return java.util.Base64.getDecoder().decode(base64Screenshot);
            }
        } catch (Exception e) {
            logger.error("Failed to attach screenshot to Allure: {}", e.getMessage(), e);
        }
        return new byte[0];
    }
    
    @Attachment(value = "Page Source", type = "text/html")
    public String attachPageSource() {
        try {
            return com.automation.core.DriverManager.getPage().content();
        } catch (Exception e) {
            logger.error("Failed to attach page source to Allure: {}", e.getMessage(), e);
            return "Failed to capture page source: " + e.getMessage();
        }
    }
}
