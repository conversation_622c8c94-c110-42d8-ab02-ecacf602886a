package com.automation.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Configuration Manager for handling application properties
 * Supports multiple environments and property sources
 */
public class ConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static Properties properties = new Properties();
    private static boolean isLoaded = false;
    
    // Default configuration file
    private static final String DEFAULT_CONFIG_FILE = "config.properties";
    
    /**
     * Load configuration from properties file
     */
    public static void loadConfiguration() {
        if (isLoaded) {
            logger.info("Configuration already loaded");
            return;
        }
        
        try {
            // Load default configuration
            loadPropertiesFile(DEFAULT_CONFIG_FILE);
            
            // Load environment-specific configuration
            String environment = System.getProperty("environment", "dev");
            String envConfigFile = "config-" + environment + ".properties";
            loadPropertiesFile(envConfigFile);
            
            // Override with system properties
            overrideWithSystemProperties();
            
            isLoaded = true;
            logger.info("Configuration loaded successfully for environment: {}", environment);
            
        } catch (Exception e) {
            logger.error("Failed to load configuration: {}", e.getMessage(), e);
            throw new RuntimeException("Configuration loading failed", e);
        }
    }
    
    /**
     * Load properties from file
     */
    private static void loadPropertiesFile(String fileName) {
        try (InputStream inputStream = getInputStream(fileName)) {
            if (inputStream != null) {
                Properties tempProps = new Properties();
                tempProps.load(inputStream);
                properties.putAll(tempProps);
                logger.info("Loaded properties from: {}", fileName);
            } else {
                logger.warn("Properties file not found: {}", fileName);
            }
        } catch (IOException e) {
            logger.warn("Could not load properties file: {} - {}", fileName, e.getMessage());
        }
    }
    
    /**
     * Get input stream for properties file
     */
    private static InputStream getInputStream(String fileName) {
        // Try to load from classpath first
        InputStream inputStream = ConfigManager.class.getClassLoader().getResourceAsStream(fileName);
        
        if (inputStream == null) {
            // Try to load from file system
            try {
                inputStream = new FileInputStream("src/main/resources/" + fileName);
            } catch (Exception e) {
                logger.debug("Could not load from file system: {}", fileName);
            }
        }
        
        return inputStream;
    }
    
    /**
     * Override properties with system properties
     */
    private static void overrideWithSystemProperties() {
        System.getProperties().forEach((key, value) -> {
            if (key.toString().startsWith("automation.")) {
                String propertyKey = key.toString().replace("automation.", "");
                properties.setProperty(propertyKey, value.toString());
                logger.debug("Override property: {} = {}", propertyKey, value);
            }
        });
    }
    
    /**
     * Get property value by key
     */
    public static String getProperty(String key) {
        if (!isLoaded) {
            loadConfiguration();
        }
        return properties.getProperty(key);
    }
    
    /**
     * Get property value by key with default value
     */
    public static String getProperty(String key, String defaultValue) {
        if (!isLoaded) {
            loadConfiguration();
        }
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * Get integer property value
     */
    public static int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid integer value for property {}: {}", key, value);
            }
        }
        return defaultValue;
    }
    
    /**
     * Get boolean property value
     */
    public static boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
    
    /**
     * Get long property value
     */
    public static long getLongProperty(String key, long defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            try {
                return Long.parseLong(value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid long value for property {}: {}", key, value);
            }
        }
        return defaultValue;
    }
    
    /**
     * Set property value
     */
    public static void setProperty(String key, String value) {
        properties.setProperty(key, value);
        logger.debug("Set property: {} = {}", key, value);
    }
    
    /**
     * Check if property exists
     */
    public static boolean hasProperty(String key) {
        if (!isLoaded) {
            loadConfiguration();
        }
        return properties.containsKey(key);
    }
    
    /**
     * Get all properties
     */
    public static Properties getAllProperties() {
        if (!isLoaded) {
            loadConfiguration();
        }
        return new Properties(properties);
    }
    
    /**
     * Get base URL for the application
     */
    public static String getBaseUrl() {
        return getProperty("base.url", "https://example.com");
    }
    
    /**
     * Get browser name
     */
    public static String getBrowser() {
        return getProperty("browser", "chromium");
    }
    
    /**
     * Get headless mode setting
     */
    public static boolean isHeadless() {
        return getBooleanProperty("headless", false);
    }
    
    /**
     * Get timeout values
     */
    public static long getDefaultTimeout() {
        return getLongProperty("default.timeout", 30000);
    }
    
    public static long getPageLoadTimeout() {
        return getLongProperty("page.load.timeout", 60000);
    }
    
    public static long getElementTimeout() {
        return getLongProperty("element.timeout", 10000);
    }
    
    /**
     * Get API configuration
     */
    public static String getApiBaseUrl() {
        return getProperty("api.base.url", "https://api.example.com");
    }
    
    public static String getApiKey() {
        return getProperty("api.key", "");
    }
    
    /**
     * Get database configuration
     */
    public static String getDatabaseUrl() {
        return getProperty("database.url", "");
    }
    
    public static String getDatabaseUsername() {
        return getProperty("database.username", "");
    }
    
    public static String getDatabasePassword() {
        return getProperty("database.password", "");
    }
    
    /**
     * Get AI/LLM configuration
     */
    public static String getOpenAiApiKey() {
        return getProperty("openai.api.key", "");
    }
    
    public static String getAugmentApiKey() {
        return getProperty("augment.api.key", "");
    }
    
    public static String getAugmentBaseUrl() {
        return getProperty("augment.base.url", "https://api.augmentcode.com");
    }
    
    /**
     * Get test data configuration
     */
    public static String getTestDataPath() {
        return getProperty("test.data.path", "src/test/resources/testdata");
    }
    
    /**
     * Get reporting configuration
     */
    public static String getReportsPath() {
        return getProperty("reports.path", "test-results/reports");
    }
    
    public static String getScreenshotsPath() {
        return getProperty("screenshots.path", "test-results/screenshots");
    }
    
    /**
     * Print all loaded properties (for debugging)
     */
    public static void printAllProperties() {
        if (!isLoaded) {
            loadConfiguration();
        }
        
        logger.info("=== Loaded Configuration Properties ===");
        properties.forEach((key, value) -> {
            // Mask sensitive information
            String displayValue = key.toString().toLowerCase().contains("password") || 
                                key.toString().toLowerCase().contains("key") ? "***" : value.toString();
            logger.info("{} = {}", key, displayValue);
        });
        logger.info("=== End Configuration Properties ===");
    }
}
