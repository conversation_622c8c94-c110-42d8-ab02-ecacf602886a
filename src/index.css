/* index.css */

body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background-color: #0f172a;
  color: #f1f5f9;
}

.dashboard-container {
  padding: 2rem;
}

.tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.tabs button {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 8px;
  background-color: #1e293b;
  color: #f1f5f9;
  cursor: pointer;
  transition: 0.2s ease;
}

.tabs button:hover {
  background-color: #334155;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.card {
  background-color: #1e293b;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 0 10px rgba(255, 0, 128, 0.2);
}

h3, h4 {
  margin-top: 0;
  color: #f472b6;
}

.test-case-item {
  padding: 0.5rem;
  background-color: #334155;
  border-radius: 6px;
  margin: 0.4rem 0;
}

.run-button {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.run-button button {
  background-color: #e11d8f;
  color: white;
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.run-button button:hover {
  background-color: #be185d;
}

.log-item {
  background-color: #0f172a;
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-left: 3px solid #f472b6;
  border-radius: 4px;
}

.log-header {
  font-weight: bold;
  cursor: pointer;
  color: #f472b6;
}

.log-content {
  margin-top: 0.5rem;
  color: #cbd5e1;
  background-color: #1e293b;
  padding: 0.5rem;
  border-radius: 6px;
}

.loader {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(15, 23, 42, 0.8);
  z-index: 999;
  justify-content: center;
  align-items: center;
}

.loader.active {
  display: flex;
}

.spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #e11d8f;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* General Button Styles */
.filter-btn {
  background: linear-gradient(to right, #f472b6, #d946ef);  /* Gradient similar to other buttons */
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 9999px;  /* Fully rounded */
  text-align: center;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  opacity: 0.9;  /* Slightly fade on hover */
}

/* Active Button Styles */
.filter-btn.active {
  background: linear-gradient(to right, #ec4899, #9d174d);  /* Darker gradient when active */
}

.filter-btn:focus {
  outline: none;  /* Remove outline */
}

/* Spacing between buttons */
.flex {
  display: flex;
  gap: 16px;  /* Adjust space between buttons */
}

.filter-buttons{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}