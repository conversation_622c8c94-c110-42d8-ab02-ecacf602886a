{"expand": "renderedFields,names,schema,operations,editmeta,changelog,versionedRepresentations", "id": "11111885", "self": "https://jira.telekom.de/rest/api/2/issue/11111885", "key": "MMKC2-11823", "fields": {"customfield_22852": null, "customfield_48448": null, "customfield_48449": null, "customfield_26417": null, "customfield_48446": null, "customfield_48447": null, "customfield_48444": null, "customfield_48445": null, "customfield_48442": null, "customfield_48443": null, "customfield_48440": null, "customfield_48441": null, "customfield_32413": null, "resolution": null, "customfield_10750": null, "customfield_32415": null, "customfield_74616": null, "customfield_32414": null, "customfield_10510": null, "customfield_10500": null, "customfield_22846": null, "customfield_44512": null, "customfield_48439": null, "customfield_48459": "", "lastViewed": "2025-05-09T11:39:50.314+0200", "customfield_48458": "", "customfield_48451": null, "customfield_55526": null, "customfield_48452": {"self": "https://jira.telekom.de/rest/api/2/customFieldOption/75370", "value": "Kommunikationsbedarf noch nicht ermittelt", "id": "75370", "disabled": false}, "customfield_48450": null, "customfield_10741": null, "labels": ["Harmonia", "TechEnablers", "harmonia"], "customfield_22719": null, "customfield_22717": null, "customfield_22716": null, "customfield_22715": null, "issuelinks": [{"id": "11220949", "self": "https://jira.telekom.de/rest/api/2/issueLink/11220949", "type": {"id": "10220", "name": "<PERSON><PERSON><PERSON>", "inward": "cloned to", "outward": "cloned from", "self": "https://jira.telekom.de/rest/api/2/issueLinkType/10220"}, "outwardIssue": {"id": "10836538", "key": "MMKC2-10955", "self": "https://jira.telekom.de/rest/api/2/issue/10836538", "fields": {"summary": "[FE] [Harmonia] StartPage | SonarQube Tech Debt", "status": {"self": "https://jira.telekom.de/rest/api/2/status/4", "description": "This issue was once resolved, but the resolution was deemed incorrect. From here issues are either marked assigned or resolved.", "iconUrl": "https://jira.telekom.de/images/icons/statuses/reopened.png", "name": "Reopened", "id": "4", "statusCategory": {"self": "https://jira.telekom.de/rest/api/2/statuscategory/2", "id": 2, "key": "new", "colorName": "default", "name": "To Do"}}, "priority": {"self": "https://jira.telekom.de/rest/api/2/priority/11800", "iconUrl": "https://jira.telekom.de/images/icons/priorities/high.svg", "name": "Major", "id": "11800"}, "issuetype": {"self": "https://jira.telekom.de/rest/api/2/issuetype/69", "id": "69", "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.", "iconUrl": "https://jira.telekom.de/secure/viewavatar?size=xsmall&avatarId=18255&avatarType=issuetype", "name": "Story", "subtask": false, "avatarId": 18255}}}}, {"id": "11220996", "self": "https://jira.telekom.de/rest/api/2/issueLink/11220996", "type": {"id": "10220", "name": "<PERSON><PERSON><PERSON>", "inward": "cloned to", "outward": "cloned from", "self": "https://jira.telekom.de/rest/api/2/issueLinkType/10220"}, "inwardIssue": {"id": "11111902", "key": "MMKC2-11824", "self": "https://jira.telekom.de/rest/api/2/issue/11111902", "fields": {"summary": "[FE] [Harmonia] StartPage | SonarQube Tech Debt part  - preparation ", "status": {"self": "https://jira.telekom.de/rest/api/2/status/3", "description": "This issue is being actively worked on at the moment by the assignee.", "iconUrl": "https://jira.telekom.de/images/icons/statuses/inprogress.png", "name": "In Progress", "id": "3", "statusCategory": {"self": "https://jira.telekom.de/rest/api/2/statuscategory/4", "id": 4, "key": "indeterminate", "colorName": "inprogress", "name": "In Progress"}}, "priority": {"self": "https://jira.telekom.de/rest/api/2/priority/11800", "iconUrl": "https://jira.telekom.de/images/icons/priorities/high.svg", "name": "Major", "id": "11800"}, "issuetype": {"self": "https://jira.telekom.de/rest/api/2/issuetype/69", "id": "69", "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.", "iconUrl": "https://jira.telekom.de/secure/viewavatar?size=xsmall&avatarId=18255&avatarType=issuetype", "name": "Story", "subtask": false, "avatarId": 18255}}}}], "customfield_22714": null, "assignee": {"self": "https://jira.telekom.de/rest/api/2/user?username=iro.papadopoulou%40telekom.com", "name": "<EMAIL>", "key": "JIRAUSER197701", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://jira.telekom.de/secure/useravatar?ownerId=JIRAUSER197701&avatarId=192641", "24x24": "https://jira.telekom.de/secure/useravatar?size=small&ownerId=JIRAUSER197701&avatarId=192641", "16x16": "https://jira.telekom.de/secure/useravatar?size=xsmall&ownerId=JIRAUSER197701&avatarId=192641", "32x32": "https://jira.telekom.de/secure/useravatar?size=medium&ownerId=JIRAUSER197701&avatarId=192641"}, "displayName": "<PERSON><PERSON>", "active": true, "timeZone": "Europe/Berlin"}, "customfield_64910": null, "customfield_22713": null, "customfield_64911": "0/0 - <PERSON>", "customfield_22712": null, "customfield_22711": null, "customfield_22710": null, "components": [], "customfield_14410": "2|j9a6y7:zzxzy", "customfield_39511": null, "customfield_48463": null, "customfield_14411": "0|2as8lu:", "customfield_48460": null, "customfield_51511": null, "customfield_48461": null, "customfield_10720": null, "customfield_10723": null, "customfield_27410": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@1298b3e3[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@a972a1f[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2984c355[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@1a70c3a8[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@24261394[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@79a99d1f[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@76b87714[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@6ac997e8[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7e679396[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@4b93b820[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7e267691[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@5876d86a[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":false}}", "customfield_67418": null, "customfield_23614": null, "customfield_44313": null, "customfield_74720": null, "customfield_48478": null, "subtasks": [], "customfield_48475": null, "customfield_10160": null, "customfield_48476": null, "customfield_42011": null, "customfield_48473": null, "customfield_48474": null, "customfield_48471": "11111885", "reporter": {"self": "https://jira.telekom.de/rest/api/2/user?username=akhil.chawla%40telekom-digital.com", "name": "<EMAIL>", "key": "JIRAUSER259696", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://jira.telekom.de/secure/useravatar?avatarId=10362", "24x24": "https://jira.telekom.de/secure/useravatar?size=small&avatarId=10362", "16x16": "https://jira.telekom.de/secure/useravatar?size=xsmall&avatarId=10362", "32x32": "https://jira.telekom.de/secure/useravatar?size=medium&avatarId=10362"}, "displayName": "<PERSON><PERSON><PERSON>", "active": true, "timeZone": "Europe/Berlin"}, "customfield_48472": null, "customfield_36810": null, "customfield_10710": null, "customfield_10711": null, "customfield_78410": null, "customfield_36014": null, "customfield_58021": null, "customfield_20514": null, "votes": {"self": "https://jira.telekom.de/rest/api/2/issue/MMKC2-11823/votes", "votes": 0, "hasVoted": false}, "customfield_58022": null, "archivedby": null, "customfield_20511": null, "issuetype": {"self": "https://jira.telekom.de/rest/api/2/issuetype/69", "id": "69", "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.", "iconUrl": "https://jira.telekom.de/secure/viewavatar?size=xsmall&avatarId=18255&avatarType=issuetype", "name": "Story", "subtask": false, "avatarId": 18255}, "customfield_48488": null, "customfield_48489": null, "customfield_48485": null, "project": {"self": "https://jira.telekom.de/rest/api/2/project/88060", "id": "88060", "key": "MMKC2", "name": "MMKC2", "projectTypeKey": "software", "avatarUrls": {"48x48": "https://jira.telekom.de/secure/projectavatar?pid=88060&avatarId=18144", "24x24": "https://jira.telekom.de/secure/projectavatar?size=small&pid=88060&avatarId=18144", "16x16": "https://jira.telekom.de/secure/projectavatar?size=xsmall&pid=88060&avatarId=18144", "32x32": "https://jira.telekom.de/secure/projectavatar?size=medium&pid=88060&avatarId=18144"}}, "customfield_48483": null, "customfield_48481": null, "resolutiondate": null, "customfield_22127": null, "watches": {"self": "https://jira.telekom.de/rest/api/2/issue/MMKC2-11823/watchers", "watchCount": 0, "isWatching": false}, "customfield_48499": null, "customfield_48498": null, "customfield_48495": null, "customfield_48496": null, "customfield_36828": null, "customfield_48494": null, "customfield_48491": null, "customfield_48492": null, "customfield_36824": null, "customfield_48490": null, "customfield_15711": null, "customfield_36711": null, "customfield_78110": null, "customfield_23521": null, "updated": "2025-04-28T12:52:32.713+0200", "customfield_49119": null, "customfield_74756": null, "customfield_49114": null, "customfield_18416": null, "description": " \r\n * *AC* - Increase code coverage to 80 percent for the assigned module. (*g)", "customfield_12310": "", "customfield_59715": null, "customfield_59714": null, "customfield_12311": null, "customfield_59716": null, "customfield_10015": null, "customfield_10800": ["<EMAIL>(JIRAUSER259696)", "<EMAIL>(JIRAUSER197701)"], "customfield_42710": null, "customfield_10927": null, "customfield_10928": null, "customfield_25038": [], "summary": "[FE] [Harmonia] StartPage | SonarQube Tech Debt part II - code coverage", "customfield_74889": null, "customfield_46411": null, "customfield_48711": null, "customfield_21010": null, "customfield_52511": null, "customfield_77913": null, "customfield_59711": null, "customfield_59713": null, "environment": null, "customfield_59712": null, "duedate": null, "customfield_28556": null, "comment": {"comments": [], "maxResults": 0, "total": 0, "startAt": 0}, "customfield_21922": null, "customfield_21921": null, "customfield_24510": null, "customfield_21920": null, "customfield_52429": null, "customfield_74216": null, "customfield_74214": null, "customfield_74215": null, "customfield_74212": null, "customfield_74213": null, "customfield_18515": null, "fixVersions": [], "customfield_18514": null, "customfield_21915": null, "customfield_40313": null, "customfield_25057": null, "customfield_25058": null, "customfield_48732": 0, "customfield_21112": null, "customfield_21111": null, "customfield_48730": null, "customfield_21110": null, "customfield_48731": null, "customfield_33917": null, "customfield_17413": {"self": "https://jira.telekom.de/rest/api/2/customFieldOption/29806", "value": "Regression Test", "id": "29806", "disabled": false}, "priority": {"self": "https://jira.telekom.de/rest/api/2/priority/11800", "iconUrl": "https://jira.telekom.de/images/icons/priorities/high.svg", "name": "Major", "id": "11800"}, "customfield_31610": null, "customfield_10465": null, "versions": [], "customfield_57310": null, "status": {"self": "https://jira.telekom.de/rest/api/2/status/10113", "description": "This status is managed internally by JIRA Software", "iconUrl": "https://jira.telekom.de/images/icons/subtask.gif", "name": "Backlog", "id": "10113", "statusCategory": {"self": "https://jira.telekom.de/rest/api/2/statuscategory/2", "id": 2, "key": "new", "colorName": "default", "name": "To Do"}}, "customfield_48502": null, "customfield_14010": null, "customfield_17640": null, "customfield_15343": null, "customfield_10693": null, "customfield_10331": {"self": "https://jira.telekom.de/rest/api/2/customFieldOption/11113", "value": "no", "id": "11113", "disabled": false}, "customfield_10333": null, "customfield_15347": null, "archiveddate": null, "customfield_37610": null, "customfield_27013": null, "customfield_27014": null, "customfield_22668": null, "customfield_22545": {"count": 0, "statuses": [{"id": 0, "name": "PASS", "description": "The test run/iteration has passed", "isFinal": true, "color": "#95C160", "isNative": true, "statusCount": 0, "statusPercent": 0}, {"id": 3, "name": "FAIL", "description": "The test run/iteration has failed", "isFinal": true, "color": "#D45D52", "isNative": true, "statusCount": 0, "statusPercent": 0}, {"id": 4, "name": "ABORTED", "description": "The test run/iteration was aborted", "isFinal": true, "color": "#111111", "isNative": true, "statusCount": 0, "statusPercent": 0}, {"id": 1006, "name": "AUTO_PASS", "description": "Automated test run passed.", "isFinal": true, "color": "#1CA62F", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1005, "name": "AUTO_MANUAL_PASS", "description": "Automatad test run passed after reexecuting manually.", "isFinal": true, "color": "#69ED11", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1000, "name": "SKIPPED", "description": "The test run was skipped", "isFinal": true, "color": "#FFA500", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 2, "name": "EXECUTING", "description": "The test run/iteration is currently being executed", "isFinal": false, "color": "#F1E069", "isNative": true, "statusCount": 0, "statusPercent": 0}, {"id": 1007, "name": "AUTO_FAIL", "description": "Automated test run failed.", "isFinal": false, "color": "#E61042", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1004, "name": "POSTPONED", "description": "The test run is postponed.", "isFinal": false, "color": "#B436EB", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1001, "name": "BLOCKED_BY_DEFECT", "description": "The test run is blocked by an existing defect.", "isFinal": false, "color": "#43BAB2", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1002, "name": "BLOCKED_IMPEDIMENT", "description": "The test run is blocked by an impediment.", "isFinal": false, "color": "#38A9FF", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1003, "name": "CLARIFICATION", "description": "Clarification is needed to execute or evaluate the test run.", "isFinal": false, "color": "#1726FF", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1009, "name": "BLOCKED", "description": "The Test run is blocked", "isFinal": false, "color": "#2800FF", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1008, "name": "WAITING", "description": "The execution of this test run is waiting for external condition.", "isFinal": false, "color": "#7F00FF", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1010, "name": "LIMITED", "description": "", "isFinal": false, "color": "#FF0088", "isNative": false, "statusCount": 0, "statusPercent": 0}, {"id": 1, "name": "TODO", "description": "The test run/iteration has not started", "isFinal": false, "color": "#A2A6AE", "isNative": true, "statusCount": 0, "statusPercent": 0}]}, "creator": {"self": "https://jira.telekom.de/rest/api/2/user?username=iro.papadopoulou%40telekom.com", "name": "<EMAIL>", "key": "JIRAUSER197701", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://jira.telekom.de/secure/useravatar?ownerId=JIRAUSER197701&avatarId=192641", "24x24": "https://jira.telekom.de/secure/useravatar?size=small&ownerId=JIRAUSER197701&avatarId=192641", "16x16": "https://jira.telekom.de/secure/useravatar?size=xsmall&ownerId=JIRAUSER197701&avatarId=192641", "32x32": "https://jira.telekom.de/secure/useravatar?size=medium&ownerId=JIRAUSER197701&avatarId=192641"}, "displayName": "<PERSON><PERSON>", "active": true, "timeZone": "Europe/Berlin"}, "customfield_22662": null, "customfield_22661": null, "customfield_52433": null, "customfield_38710": null, "customfield_31511": null, "customfield_11410": "9223372036854775807", "customfield_19810": null, "customfield_52438": null, "customfield_28113": null, "customfield_57211": null, "customfield_28110": null, "customfield_28111": null, "customfield_74210": null, "customfield_28116": null, "customfield_74211": null, "customfield_48509": null, "customfield_28114": null, "customfield_28115": null, "customfield_48507": null, "customfield_48508": null, "customfield_62910": null, "customfield_48505": null, "customfield_56710": null, "customfield_37510": "", "customfield_27153": null, "workratio": -1, "customfield_75110": null, "customfield_27716": null, "customfield_48416": null, "customfield_27715": null, "customfield_25419": null, "customfield_27717": null, "customfield_73610": null, "customfield_31419": null, "customfield_65411": null, "customfield_18142": null, "created": "2025-02-04T11:24:21.977+0100", "customfield_65410": null, "customfield_33713": null, "customfield_17615": "<a title='[TechEnablers] - StartPage | SonarQube Tech Debt Analysis and Remediation' href='https://jira.telekom.de/browse/MMKC2-10950' class='aui-label null' style='color: #3572b0; background-color: null; border-color: null'>[TechEnablers] - StartPage | SonarQube Tech Debt Analysis and Remediation</a>", "customfield_56024": null, "customfield_31421": null, "customfield_31420": null, "customfield_53310": null, "customfield_34011": null, "customfield_27169": null, "customfield_48426": null, "customfield_18255": null, "customfield_18256": null, "customfield_48425": null, "customfield_18257": null, "customfield_48422": null, "customfield_18130": [], "customfield_47210": null, "customfield_18131": null, "customfield_48420": null, "customfield_48421": null, "customfield_14212": null, "customfield_32512": null, "customfield_18258": null, "customfield_10771": null, "customfield_31422": null, "customfield_18259": null, "customfield_33610": null, "customfield_35355": "9223372036854775807", "attachment": [], "customfield_29111": null, "customfield_48419": null, "customfield_48417": null, "customfield_79010": null, "customfield_48418": null, "customfield_73712": null, "customfield_48437": null, "customfield_73713": null, "customfield_48438": null, "customfield_73710": null, "customfield_44510": null, "customfield_48435": null, "customfield_73711": null, "customfield_48436": null, "customfield_44511": null, "customfield_48433": null, "customfield_48434": null, "customfield_67611": null, "customfield_48431": null, "customfield_48432": null, "customfield_73714": null, "customfield_73715": null, "customfield_18129": null, "customfield_12811": "MMKC2-10950", "customfield_12810": ["com.atlassian.greenhopper.service.sprint.Sprint@51d89621[id=148174,rapidViewId=42918,state=FUTURE,name=Backlog New,startDate=<null>,endDate=<null>,completeDate=<null>,activatedDate=<null>,sequence=148174,goal=<null>,synced=false,autoStartStop=false,incompleteIssuesDestinationId=<null>]"]}}