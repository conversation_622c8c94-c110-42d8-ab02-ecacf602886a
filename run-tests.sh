#!/bin/bash

# Playwright Automation Framework Test Execution Script
# This script provides various options for running tests

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
BROWSER="chromium"
HEADLESS="false"
ENVIRONMENT="dev"
TEST_SUITE="all"
PARALLEL="true"
THREAD_COUNT="3"
GENERATE_REPORT="true"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -b, --browser <PERSON><PERSON>SE<PERSON>      Browser to use (chromium, firefox, webkit) [default: chromium]"
    echo "  -h, --headless             Run in headless mode [default: false]"
    echo "  -e, --environment ENV      Environment to test (dev, staging, prod) [default: dev]"
    echo "  -s, --suite SUITE          Test suite to run (smoke, regression, generated, api, all) [default: all]"
    echo "  -p, --parallel             Enable parallel execution [default: true]"
    echo "  -t, --threads COUNT        Number of parallel threads [default: 3]"
    echo "  -r, --report               Generate Allure report after execution [default: true]"
    echo "  --no-install               Skip Playwright browser installation"
    echo "  --clean                    Clean previous test results"
    echo "  --debug                    Enable debug logging"
    echo "  --help                     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests with default settings"
    echo "  $0 -b firefox -h                     # Run in Firefox headless mode"
    echo "  $0 -s smoke -e staging               # Run smoke tests in staging environment"
    echo "  $0 -s regression -p -t 5             # Run regression tests with 5 parallel threads"
    echo "  $0 --clean -s api                    # Clean and run API tests"
}

# Function to install Playwright browsers
install_browsers() {
    print_info "Installing Playwright browsers..."
    mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install" -q
    if [ $? -eq 0 ]; then
        print_success "Playwright browsers installed successfully"
    else
        print_error "Failed to install Playwright browsers"
        exit 1
    fi
}

# Function to clean previous results
clean_results() {
    print_info "Cleaning previous test results..."
    rm -rf test-results/
    rm -rf target/allure-results/
    rm -rf target/surefire-reports/
    mkdir -p test-results/{reports,screenshots,videos,logs}
    print_success "Previous test results cleaned"
}

# Function to validate Java and Maven
validate_prerequisites() {
    print_info "Validating prerequisites..."
    
    # Check Java
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1-2)
    print_info "Java version: $JAVA_VERSION"
    
    # Check Maven
    if ! command -v mvn &> /dev/null; then
        print_error "Maven is not installed or not in PATH"
        exit 1
    fi
    
    MVN_VERSION=$(mvn -version | head -n 1 | cut -d' ' -f3)
    print_info "Maven version: $MVN_VERSION"
    
    print_success "Prerequisites validated"
}

# Function to build the project
build_project() {
    print_info "Building the project..."
    mvn clean compile test-compile -q
    if [ $? -eq 0 ]; then
        print_success "Project built successfully"
    else
        print_error "Failed to build project"
        exit 1
    fi
}

# Function to run tests
run_tests() {
    print_info "Starting test execution..."
    print_info "Configuration:"
    print_info "  Browser: $BROWSER"
    print_info "  Headless: $HEADLESS"
    print_info "  Environment: $ENVIRONMENT"
    print_info "  Test Suite: $TEST_SUITE"
    print_info "  Parallel: $PARALLEL"
    print_info "  Thread Count: $THREAD_COUNT"
    
    # Build Maven command
    MVN_CMD="mvn test"
    MVN_CMD="$MVN_CMD -Dautomation.browser=$BROWSER"
    MVN_CMD="$MVN_CMD -Dautomation.headless=$HEADLESS"
    MVN_CMD="$MVN_CMD -Denvironment=$ENVIRONMENT"
    
    # Add debug logging if enabled
    if [ "$DEBUG" = "true" ]; then
        MVN_CMD="$MVN_CMD -Dautomation.log.level=DEBUG"
    fi
    
    # Set test suite
    case $TEST_SUITE in
        "smoke")
            MVN_CMD="$MVN_CMD -Dtest=SmokeTests"
            ;;
        "regression")
            MVN_CMD="$MVN_CMD -Dtest=RegressionTests"
            ;;
        "generated")
            MVN_CMD="$MVN_CMD -Dtest=GeneratedTests"
            ;;
        "api")
            MVN_CMD="$MVN_CMD -Dtest=APITests"
            ;;
        "all")
            # Run all tests - no specific test filter
            ;;
        *)
            print_error "Invalid test suite: $TEST_SUITE"
            exit 1
            ;;
    esac
    
    # Execute tests
    print_info "Executing command: $MVN_CMD"
    eval $MVN_CMD
    
    TEST_EXIT_CODE=$?
    
    if [ $TEST_EXIT_CODE -eq 0 ]; then
        print_success "All tests passed!"
    else
        print_warning "Some tests failed or were skipped (exit code: $TEST_EXIT_CODE)"
    fi
    
    return $TEST_EXIT_CODE
}

# Function to generate Allure report
generate_allure_report() {
    if [ "$GENERATE_REPORT" = "true" ]; then
        print_info "Generating Allure report..."
        
        if command -v allure &> /dev/null; then
            allure generate target/allure-results --clean -o target/allure-report
            print_success "Allure report generated at: target/allure-report/index.html"
            
            # Ask if user wants to open the report
            read -p "Do you want to open the Allure report? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                allure open target/allure-report
            fi
        else
            print_warning "Allure CLI not found. Install it to generate Allure reports."
            print_info "You can still view ExtentReports in test-results/reports/"
        fi
    fi
}

# Function to show test summary
show_test_summary() {
    print_info "Test Execution Summary:"
    
    # Count test results from surefire reports if available
    if [ -d "target/surefire-reports" ]; then
        TOTAL_TESTS=$(find target/surefire-reports -name "*.xml" -exec grep -l "testcase" {} \; | wargs grep -o "testcase" | wc -l 2>/dev/null || echo "N/A")
        FAILED_TESTS=$(find target/surefire-reports -name "*.xml" -exec grep -l "failure\|error" {} \; | wargs grep -o "failure\|error" | wc -l 2>/dev/null || echo "N/A")
        
        print_info "  Total Tests: $TOTAL_TESTS"
        print_info "  Failed Tests: $FAILED_TESTS"
    fi
    
    # Show report locations
    print_info "Reports available at:"
    if [ -d "test-results/reports" ]; then
        EXTENT_REPORT=$(find test-results/reports -name "ExtentReport_*.html" | head -1)
        if [ -n "$EXTENT_REPORT" ]; then
            print_info "  ExtentReport: $EXTENT_REPORT"
        fi
    fi
    
    if [ -d "target/allure-report" ]; then
        print_info "  Allure Report: target/allure-report/index.html"
    fi
    
    if [ -d "test-results/screenshots" ]; then
        SCREENSHOT_COUNT=$(find test-results/screenshots -name "*.png" | wc -l)
        print_info "  Screenshots: $SCREENSHOT_COUNT files in test-results/screenshots/"
    fi
}

# Parse command line arguments
SKIP_INSTALL="false"
CLEAN="false"
DEBUG="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--browser)
            BROWSER="$2"
            shift 2
            ;;
        -h|--headless)
            HEADLESS="true"
            shift
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -s|--suite)
            TEST_SUITE="$2"
            shift 2
            ;;
        -p|--parallel)
            PARALLEL="true"
            shift
            ;;
        -t|--threads)
            THREAD_COUNT="$2"
            shift 2
            ;;
        -r|--report)
            GENERATE_REPORT="true"
            shift
            ;;
        --no-install)
            SKIP_INSTALL="true"
            shift
            ;;
        --clean)
            CLEAN="true"
            shift
            ;;
        --debug)
            DEBUG="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_info "Playwright Automation Framework Test Runner"
    print_info "============================================"
    
    # Validate prerequisites
    validate_prerequisites
    
    # Clean if requested
    if [ "$CLEAN" = "true" ]; then
        clean_results
    fi
    
    # Install browsers if not skipped
    if [ "$SKIP_INSTALL" = "false" ]; then
        install_browsers
    fi
    
    # Build project
    build_project
    
    # Run tests
    run_tests
    TEST_RESULT=$?
    
    # Generate reports
    generate_allure_report
    
    # Show summary
    show_test_summary
    
    # Exit with test result code
    if [ $TEST_RESULT -eq 0 ]; then
        print_success "Test execution completed successfully!"
    else
        print_warning "Test execution completed with failures"
    fi
    
    exit $TEST_RESULT
}

# Run main function
main
