<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="PlaywrightAutomationSuite" verbose="1" parallel="methods" thread-count="3">
    
    <parameter name="browser" value="chromium"/>
    <parameter name="headless" value="false"/>
    <parameter name="environment" value="dev"/>
    
    <listeners>
        <listener class-name="com.automation.listeners.TestListener"/>
        <listener class-name="com.automation.listeners.AllureListener"/>
        <listener class-name="com.automation.listeners.ExtentReportListener"/>
    </listeners>
    
    <!-- Smoke Test Suite -->
    <test name="SmokeTests" preserve-order="true">
        <parameter name="testType" value="smoke"/>
        <classes>
            <class name="com.automation.tests.SmokeTests"/>
        </classes>
    </test>
    
    <!-- Regression Test Suite -->
    <test name="RegressionTests" preserve-order="true">
        <parameter name="testType" value="regression"/>
        <classes>
            <class name="com.automation.tests.RegressionTests"/>
        </classes>
    </test>
    
    <!-- Generated Test Suite -->
    <test name="GeneratedTests" preserve-order="true">
        <parameter name="testType" value="generated"/>
        <classes>
            <class name="com.automation.tests.GeneratedTests"/>
        </classes>
    </test>
    
    <!-- API Test Suite -->
    <test name="APITests" preserve-order="true">
        <parameter name="testType" value="api"/>
        <classes>
            <class name="com.automation.tests.APITests"/>
        </classes>
    </test>
    
</suite>
